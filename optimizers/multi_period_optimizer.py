import numpy as np
import cvxpy as cp
from abc import ABC, abstractmethod
from cvxpy.error import SolverError

from utils import util
from utils.typing_util import float_array
from errors.errors import OptimizationError


class MultiPeriodOptimizerInterface(ABC):
    """
    Abstract base class for multi-period portfolio optimization.
    Provides methods to calculate multi-period efficient frontiers and
    cumulative returns and product_covariance matrices.

    Methods:
        calculate_multi_period_efficient_frontier: Calculates the efficient frontier over multiple periods based on risk and return trade-offs.
        calculate_multi_period_portfolio: Abstract method to optimize portfolio weights for multiple periods based on a target return.
    """

    def __init__(self):
        self.TARGET_RETURN_STEPS = 10

    def calculate_multi_period_efficient_frontier(self,
                                                  risk_free_return: float,
                                                  product_returns: float_array,
                                                  covariance: float_array,
                                                  alphas: np.ndarray,
                                                  n_risky_products: int = 2
                                                  ) -> (np.ndarray, np.ndarray, np.ndarray, np.ndarray):
        """
            Computes the multi-period efficient frontier.

            Args:
                risk_free_return (float): The risk-free rate of return.
                product_returns (float_array): Expected returns of the investment products.
                covariance (float_array): Covariance matrix of the product returns.
                alphas (np.ndarray): Cashflow weights for each period.
                n_risky_products (int): Number of risky products in the portfolio.

            Returns:
                tuple:
                    - np.ndarray: Weights of the risk-free asset.
                    - np.ndarray: Weights of the products.
                    - np.ndarray: Target returns for each portfolio on the frontier.
                    - np.ndarray: Variances corresponding to the target returns.
        """

        if isinstance(product_returns, np.ndarray) and not product_returns.shape[0] == covariance.shape[0]:
            raise ValueError('Product returns and product_covariance matrix dimensions must align.')

        min_return, max_return = self.calculate_weighted_min_and_max_cumulative_returns(risk_free_return, np.max(product_returns), alphas)

        target_returns = np.linspace(min_return, max_return, self.TARGET_RETURN_STEPS).squeeze()

        results = {"risk_free_weights": [], "products_weights": [], "returns": [], "variances": []}

        for target_return in target_returns:
            risk_free_w, products_w, return_, variance, _ = self.calculate_multi_period_strategy(risk_free_return, product_returns, covariance, alphas, target_return, n_risky_products)
            results["risk_free_weights"].append(risk_free_w)
            results["products_weights"].append(products_w)
            results["returns"].append(return_)
            results["variances"].append(variance)

        return (
            np.array(results["risk_free_weights"]),
            np.array(results["products_weights"]),
            np.array(results["returns"]).reshape(-1, 1),
            np.array(results["variances"]),
        )

    @abstractmethod
    def calculate_multi_period_strategy(self,
                                        risk_free_return: float,
                                        returns: float_array,
                                        covariance: float_array,
                                        alphas: np.ndarray,
                                        target_return: float,
                                        n_risky_products: int = 1
                                        ) -> (np.ndarray, np.ndarray, float, float, bool):
        """
        Abstract method to optimize the portfolio for multiple periods.

        Args:
            risk_free_return (float): The risk-free rate of return.
            returns (float_array): Expected returns of the investment products.
            covariance (float_array): Covariance matrix of the product returns.
            alphas (np.ndarray): Cashflow weights for each period.
            target_return (float): Target portfolio return.
            n_risky_products (int): Number of products in the portfolio.

        Returns:
            tuple:
                - np.ndarray: Weights of the risk-free asset.
                - np.ndarray: Weights of the products.
                - float: Optimized portfolio return.
                - float: FrontierPortfolio variance.
                - bool: Flag indicating whether the optimization was successful.
        """
        pass

    def calculate_cumulative_returns_and_covariance(self,
                                                    returns: float_array,
                                                    covariance: float_array,
                                                    n_periods: int
                                                    ) -> (np.ndarray, np.ndarray):
        """
        Calculates cumulative returns and product_covariance for multiple periods.

        Args:
            returns (float_array): Expected returns of the investment products.
            covariance (float_array): Covariance matrix of the product returns.
            n_periods (int): Number of periods for the optimization.

        Returns:
            tuple:
                - np.ndarray: Cumulative returns over the periods.
                - np.ndarray: Cumulative product_covariance matrix.
        """

        n_array = np.arange(n_periods, 0, -1).reshape(-1, 1)

        if isinstance(returns, float):
            returns = np.array([returns]).reshape(-1, 1)
        if isinstance(covariance, float):
            covariance = np.array([covariance])

        log_returns, log_covariance = util.get_log_mean_and_covariance_from_arithmetic(returns, covariance)

        # example: we have 3 products (x, y, z) and 2 periods. n_log_returns must be [x_2, x_1, y_2, y_1, z_2, z_1]
        n_log_returns = (n_array * log_returns.T).flatten(order='F')
        n_log_covariance = self._calculate_cumulative_covariance(log_covariance, n_periods)

        n_returns, n_covariance = util.get_arithmetic_mean_and_covariance_from_log(n_log_returns, n_log_covariance)

        return n_returns, n_covariance

    @staticmethod
    def _calculate_cumulative_covariance(covariance: np.ndarray,
                                         n_periods: int
                                         ) -> np.ndarray:
        """
        Calculates the cumulative product_covariance matrix.

        Args:
            covariance (np.ndarray): Covariance matrix of product returns.
            n_periods (int): Number of periods for the optimization.

        Returns:
                np.ndarray: Cumulative covariance matrix.
        """

        covariance_coefficients = MultiPeriodOptimizerInterface.__generate_variance_matrix_coefficients(n_periods)
        n_covariance = np.kron(covariance, covariance_coefficients)

        return n_covariance

    @staticmethod
    def __generate_variance_matrix_coefficients(n_periods: int) -> np.ndarray:
        """
        Generates variance matrix coefficients for cumulative covariance calculation.

        Args:
            n_periods (int): Number of periods for the optimization.

        Returns:
            np.ndarray: Variance matrix coefficients.
        """

        ones = [np.ones((i + 1, i + 1)) for i in range(n_periods)]
        coefficients = np.zeros_like(ones[-1])

        for i in range(n_periods):
            coefficients[:i + 1, :i + 1] += ones[i]

        return coefficients

    @staticmethod
    def calculate_weighted_min_and_max_cumulative_returns(min_return: float_array,
                                                          max_return: float_array,
                                                          alphas: np.ndarray
                                                          ) -> (float, float):
        """
        Calculates weighted minimum and maximum cumulative returns over multiple periods.

        Args:
            min_return (float): Minimum return for the portfolio.
            max_return (float_array): Maximum return for the portfolio.
            alphas (np.ndarray): Cashflow weights for each period.

        Returns:
            tuple:
                - float: Weighted minimum cumulative return.
                - float: Weighted maximum cumulative return.
        """

        if isinstance(min_return, np.ndarray):
            min_return = min_return.item()

        if isinstance(max_return, np.ndarray):
            max_return = max_return.item()

        n_periods = alphas.shape[0]
        periods = np.arange(n_periods, 0, -1)

        cumulative_returns = np.array([
            (1 + min_return) ** periods - 1,
            (1 + max_return) ** periods - 1
        ])

        target_returns = cumulative_returns @ alphas

        return target_returns[0], target_returns[-1]


class MultiPeriodHeuristic():
    def __init__(self,
                 single_period_simulated_returns: np.ndarray,
                 alphas: np.ndarray,
                 target_CVaR: float,
                 min_CVaR_index: int,
                 p: float,
                 current_portfolio: np.ndarray = None,
                 sampled_returns: np.ndarray = None):
        pass

    def calculate_multi_period_strategy(self,
                                        expected_returns: np.ndarray,
                                        covariance: np.ndarray,
                                        ):
        pass


class MultiPeriodOptimizer(MultiPeriodOptimizerInterface):
    """
    PortfolioOptimizer for portfolios with N investment products and a risk-free asset.
    Implements multi-period optimization logic for multiple assets.

    Methods:
        calculate_multi_period_portfolio: Optimizes portfolio weights for multiple assets.
    """

    def calculate_multi_period_strategy(self,
                                        risk_free_return: float,
                                        returns: float_array,
                                        covariance: float_array,
                                        alphas: np.ndarray,
                                        target_return: float,
                                        n_risky_products: int = 2
                                        ) -> (np.ndarray, np.ndarray, float, float, bool):
        """
        Optimizes the portfolio weights for multiple products over multiple periods.

        Args:
            risk_free_return (float): The risk-free rate of return.
            returns (float_array): Expected returns of the products.
            covariance (float_array): Covariance matrix of the product returns.
            alphas (np.ndarray): Cashflow weights for each period.
            target_return (float): Target portfolio return.
            n_risky_products (int): Number of products in the portfolio.

        Returns:
            tuple:
                - np.ndarray: Weights of the risk-free asset.
                - np.ndarray: Weights of the products.
                - float: Optimized portfolio return.
                - float: FrontierPortfolio variance.
                - bool: Flag indicating whether the optimization was successful.

        Raises:
            OptimizationError: If the optimization problem does not converge.
        """

        n_periods = alphas.shape[0]

        n_risk_free_returns, _ = self.calculate_cumulative_returns_and_covariance(risk_free_return, 0.0, n_periods)
        n_returns, n_covariance = self.calculate_cumulative_returns_and_covariance(returns, covariance, n_periods)
        n_covariance *= 10000
        n_covariance = cp.psd_wrap(n_covariance)

        risk_free_weights = cp.Variable(shape=(n_periods, 1))
        weights = cp.Variable(shape=(n_periods, n_risky_products))

        money_weighted_risk_free_weights = cp.multiply(alphas, risk_free_weights)
        money_weighted_weights = cp.multiply(alphas, weights).flatten(order='F')

        money_weighted_return = money_weighted_risk_free_weights.T @ n_risk_free_returns + money_weighted_weights.T @ n_returns
        money_weighted_variance = cp.quad_form(money_weighted_weights, n_covariance)

        constraints = [
            money_weighted_return >= target_return,
            cp.sum(risk_free_weights, axis=1) + cp.sum(weights, axis=1) == np.ones((n_periods,)),
            risk_free_weights >= 0,
            weights >= 0
        ]

        try:
            problem = cp.Problem(cp.Minimize(money_weighted_variance), constraints)
            problem.solve(solver=cp.GUROBI)
        except SolverError:
            #if CPLEX is used and problem is infeasible, solver will throw SolverError instead of setting status to infeasible
            print(problem.status)
            problem.solve(solver=cp.SCIP, verbose=True)


        if problem.status in ['infeasible', 'infeasible_or_unbounded']:
            risk_free_weights = np.zeros((n_periods, 1))
            weights = np.zeros((n_periods, n_risky_products))

            max_return_index = np.argmax(returns)

            weights[:, max_return_index] = 1.0

            money_weighted_risk_free_weights = alphas * risk_free_weights
            money_weighted_weights = (alphas * weights).flatten(order='F').reshape(-1, 1)

            money_weighted_return = np.round(money_weighted_risk_free_weights.T @ n_risk_free_returns + money_weighted_weights.T @ n_returns, 4)
            money_weighted_variance = np.round(money_weighted_weights.T @ n_covariance.value @ money_weighted_weights, 4)

            return risk_free_weights, weights, money_weighted_return, money_weighted_variance, False

        elif problem.status not in ['optimal', 'optimal_inaccurate']:
            print(problem.status)
            raise OptimizationError(problem.status)

        #TODO remove rounding
        risk_free_weights = np.round(risk_free_weights.value, 4)
        weights = np.round(weights.value, 4)
        money_weighted_return = np.round(money_weighted_return.value.item(), 4)
        money_weighted_variance = np.round(money_weighted_variance.value.item() / 10000, 10)

        return risk_free_weights, weights, money_weighted_return, money_weighted_variance, True


class ConstrainedMultiPeriodOptimizer(MultiPeriodOptimizerInterface):
    """
    PortfolioOptimizer for portfolios with N investment products and a risk-free asset.
    Implements multi-period optimization logic for multiple assets.

    Methods:
        calculate_multi_period_portfolio: Optimizes portfolio weights for multiple assets.
    """

    def calculate_multi_period_strategy(self,
                                        risk_free_return: float,
                                        returns: float_array,
                                        covariance: float_array,
                                        alphas: np.ndarray,
                                        target_return: float,
                                        n_risky_products: int = 1
                                        ) -> (np.ndarray, np.ndarray, float, float, bool):
        """
        Optimizes the portfolio weights for multiple products over multiple periods.

        Args:
            risk_free_return (float): The risk-free rate of return.
            returns (float_array): Expected returns of the products.
            covariance (float_array): Covariance matrix of the product returns.
            alphas (np.ndarray): Cashflow weights for each period.
            target_return (float): Target portfolio return.
            n_risky_products (int): Number of products in the portfolio.

        Returns:
            tuple:
                - np.ndarray: Weights of the risk-free asset.
                - np.ndarray: Weights of the products.
                - float: Optimized portfolio return.
                - float: FrontierPortfolio variance.
                - bool: Flag indicating whether the optimization was successful.

        Raises:
            OptimizationError: If the optimization problem does not converge.
        """

        n_periods = alphas.shape[0]

        n_risk_free_returns, _ = self.calculate_cumulative_returns_and_covariance(risk_free_return, 0.0, n_periods)
        n_returns, n_covariance = self.calculate_cumulative_returns_and_covariance(returns, covariance, n_periods)
        n_covariance = cp.psd_wrap(n_covariance)

        risk_free_weights = cp.Variable(shape=(n_periods, 1))
        weights = cp.Variable(shape=(n_periods, n_risky_products))

        money_weighted_risk_free_weights = cp.multiply(alphas, risk_free_weights)
        money_weighted_weights = cp.multiply(alphas, weights).flatten(order='F')

        money_weighted_return = money_weighted_risk_free_weights.T @ n_risk_free_returns + money_weighted_weights.T @ n_returns
        money_weighted_variance = cp.quad_form(money_weighted_weights, n_covariance)

        constraints = [
            money_weighted_return >= target_return,
            cp.sum(risk_free_weights, axis=1) + cp.sum(weights, axis=1) == np.ones((n_periods,)),
            risk_free_weights >= 0,
            weights >= 0,
            weights[1:] <= weights[:-1]
        ]

        problem = cp.Problem(cp.Minimize(money_weighted_variance), constraints)
        problem.solve(solver=cp.GUROBI)

        if problem.status not in ['optimal', 'optimal_inaccurate']:
            raise OptimizationError(problem.status)

        risk_free_weights = np.round(risk_free_weights.value, 6)
        weights = np.round(weights.value, 6)
        money_weighted_return = np.round(money_weighted_return.value.item(), 6)
        money_weighted_variance = np.round(money_weighted_variance.value.item(), 6)

        return risk_free_weights, weights, money_weighted_return, money_weighted_variance, True
