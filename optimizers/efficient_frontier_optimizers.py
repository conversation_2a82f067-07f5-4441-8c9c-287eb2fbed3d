from typing import Callable, <PERSON><PERSON>, Optional
import numpy as np
import cvxpy as cp
from abc import ABC, abstractmethod

import pandas as pd

from configuration import config, logging_config
from errors.errors import OptimizationError
from factories.return_calculations import ReturnCalculatorFactory
from factories.return_measures import ReturnMeasureFactory
from factories.risk_measures import RiskMeasureFactory
from schemas.constants import TargetMeasureEnum
from schemas.pydantic_models import UserPreferences, PreferenceBasedCalculations
from services import simulation_service
from utils.typing_util import CalculateFeasibleStrategiesType
from utils.util import round_to_sum_1

logger = logging_config.get_logger(__name__)


class PortfolioOptimizer(ABC):
    """
    Abstract base class for portfolio optimization.

    Methods
    -------
    calculate_efficient_frontier(product_returns: np.ndarray, product_covariance: np.ndarray) -> (np.ndarray, np.ndarray, np,ndarray)
        Abstract method to compute the efficient frontier for a given set of product returns and product covariance matrix.
    """

    def __init__(self):
        self.settings = config.get_settings()

    @abstractmethod
    def calculate_efficient_frontier(self, product_returns: np.ndarray, product_covariance: np.ndarray) -> (np.ndarray, np.ndarray, np.ndarray):
        pass

    def _get_min_and_max_returns(self, product_returns: np.ndarray, product_covariance: np.ndarray) -> (float, float):
        """
        Compute the minimum and maximum returns of the efficient frontier for a given set of product_returns and product_covariance matrix.

        Parameters
        ----------
        product_returns : np.ndarray
            Product expected product_returns vector.
        product_covariance : np.ndarray
            Product product_covariance matrix.

        Returns
        -------
        (float, float)
            Minimum and maximum portfolio product_returns.
        """

        min_return = (self._calculate_minimal_variance_portfolio(product_returns, product_covariance) @ product_returns).item()
        max_return = self._get_max_return(product_returns)
        return min_return, max_return

    @staticmethod
    def _calculate_minimal_variance_portfolio(product_returns: np.ndarray, product_covariance: np.ndarray) -> np.ndarray:
        """
        Compute portfolio weights that minimize variance.

        Parameters
        ----------
        product_returns : np.ndarray
            Product expected product_returns vector.
        product_covariance : np.ndarray
            Product product_covariance matrix.

        Returns
        -------
        np.ndarray
            Portfolio weights that minimize variance.

        Raises
        ------
        OptimizationError
            If the optimization problem is not solved to optimality.
        """
        n = product_returns.shape[0]

        weights = cp.Variable(n, name='weights', nonneg=True)
        variance = cp.quad_form(weights, product_covariance)

        constraints = [
            cp.sum(weights) == 1,
            cp.matmul(weights.T, product_returns) >= 5e-5
        ]

        problem = cp.Problem(objective=cp.Minimize(variance), constraints=constraints)
        problem.solve(solver=cp.SCIP)

        if problem.status not in ['optimal', 'optimal_inaccurate']:
            raise OptimizationError(problem.status)

        return weights.value

    @staticmethod
    def _get_max_return(product_returns: np.ndarray) -> float:
        """
        Compute the maximum possible return.

        Parameters
        ----------
        product_returns : np.ndarray
            Product expected product_returns vector.

        Returns
        -------
        float
            Maximum return.

        Raises
        ------
        OptimizationError
            If the optimization problem is not solved to optimality.
        """
        n = product_returns.shape[0]

        weights = cp.Variable(n)

        constraints = [
            weights >= 0,
            weights <= 0.5,
            cp.sum(weights) == 1
        ]

        problem = cp.Problem(cp.Maximize(product_returns.T @ weights), constraints)
        problem.solve(solver=cp.SCIP)

        if problem.status not in ['optimal', 'optimal_inaccurate']:
            raise OptimizationError(problem.status)

        return (product_returns.T @ weights.value).item()


class MeanVarianceOptimizer(PortfolioOptimizer):
    """
    PortfolioOptimizer using linear programming to compute the efficient frontier without risk-free.

    Methods
    -------
    calculate_efficient_frontier(product_returns: np.ndarray, product_covariance: np.ndarray) -> np.ndarray
        Compute portfolio weights for the efficient frontier.
    """

    def calculate_efficient_frontier(self, product_returns: np.ndarray, product_covariance: np.ndarray) -> (np.ndarray, np.ndarray, np.ndarray):
        """
        Compute the efficient frontier for the given product_returns and product_covariance matrix.

        Parameters
        ----------
        product_returns : np.ndarray
            Product expected product_returns vector.
        product_covariance : np.ndarray
            Product product_covariance matrix.

        Returns
        -------
        np.ndarray
            Array of portfolio weights for each target return.
        """
        logger.debug('Calculating portfolio efficient frontier')
        min_return, max_return = self._get_min_and_max_returns(product_returns, product_covariance)

        frontier_size = self.settings.default_portfolio_frontier_size
        returns_space = np.linspace(start=min_return, stop=max_return, num=frontier_size, endpoint=True)

        n = product_returns.shape[0]

        weights = cp.Variable(n)

        ret = product_returns.T @ weights
        ret_target = cp.Parameter(nonneg=True)
        risk = cp.quad_form(weights, product_covariance)

        constraints = [
            weights >= 0,
            weights <= 0.5,
            cp.sum(weights) == 1,
            ret >= ret_target
        ]

        problem = cp.Problem(objective=cp.Minimize(risk), constraints=constraints)

        portfolios = np.zeros((frontier_size, n))
        returns_ = np.zeros(frontier_size)
        variances_ = np.zeros(frontier_size)

        logger.debug(f'Return space is divided in {frontier_size} samples. Calculating portfolio for each of them')

        for i, ret_i in enumerate(returns_space):
            ret_target.value = ret_i
            problem.solve(solver=cp.CPLEX)

            if problem.status not in ['optimal', 'optimal_inaccurate']:
                raise OptimizationError(problem.status)

            portfolio_weights = round_to_sum_1(weights.value)

            portfolios[i] = portfolio_weights
            returns_[i] = ret.value
            variances_[i] = risk.value

        return portfolios, returns_, variances_


class CapmOptimizer(PortfolioOptimizer):
    """
        PortfolioOptimizer using linear programming to compute the efficient frontier with risk-free included.

        Methods
        -------
        calculate_efficient_frontier(product_returns: np.ndarray, product_covariance: np.ndarray) -> np.ndarray
            Compute portfolio weights for the efficient frontier.
        """

    def __init__(self):
        super(CapmOptimizer, self).__init__()

    def calculate_portfolio_for_target_return(self, product_returns: np.ndarray, product_covariance: np.ndarray, target_return: float) -> np.ndarray:
        """
        Compute the portfolio with target return for the given product_returns and product_covariance matrix.

        Parameters
        ----------
        product_returns : np.ndarray
            Product expected product_returns vector, with the risk-free rate included as the last element.
        product_covariance : np.ndarray
            Product product_covariance matrix, with zeros padded in the last row and column.
        target_return : float
            Target return of the portfolio.

        Returns
        -------
        np.ndarray
            Array of portfolio weights for target return.
        float
            Expected return of the portfolio.
        float
            Variance of the portfolio.
        """
        logger.debug(f'Calculating portfolio for target return: {target_return}')

        min_return, max_return = self._get_min_and_max_returns(product_returns, product_covariance)

        if target_return > max_return:
            target_return = max_return

        if target_return < min_return:
            target_return = min_return

        n = product_returns.shape[0]

        weights = cp.Variable(n)

        ret = product_returns.T @ weights

        risk = cp.quad_form(weights, product_covariance)

        constraints = [
            weights >= 0,
            weights[0:-1] <= 0.5,
            cp.sum(weights) == 1,
            ret >= target_return
        ]

        problem = cp.Problem(objective=cp.Minimize(risk), constraints=constraints)
        problem.solve(solver=cp.SCIP)

        logger.debug(f'Problem solved with status {problem.status}')

        return weights.value

    def calculate_efficient_frontier(self, product_returns: np.ndarray, product_covariance: np.ndarray) -> (np.ndarray, np.ndarray, np.ndarray):
        """
        Compute the efficient frontier for the given product_returns and product_covariance matrix.

        Parameters
        ----------
        product_returns : np.ndarray
            Expected product returns vector, with the risk-free rate included as the last element.
        product_covariance : np.ndarray
            Product covariance matrix, with zeros padded in the last row and column.

        Returns
        -------
        np.ndarray
            Array of portfolio weights for each target return.
        np.ndarray
            Array of expected product returns for each portfolio.
        np.ndarray
            Array of variances for each portfolio.
        """
        logger.debug('Calculating portfolio efficient frontier')
        min_return, max_return = self._get_min_and_max_returns(product_returns, product_covariance)

        frontier_size = self.settings.default_portfolio_frontier_size
        returns_space = np.linspace(start=min_return, stop=max_return, num=frontier_size, endpoint=True)

        n = product_returns.shape[0]

        weights = cp.Variable(n)

        ret = product_returns.T @ weights
        ret_target = cp.Parameter(nonneg=True)

        risk = cp.quad_form(weights, product_covariance)

        constraints = [
            weights >= 0,
            weights[0:-1] <= 0.5,
            cp.sum(weights) == 1,
            ret >= ret_target
        ]

        problem = cp.Problem(objective=cp.Minimize(risk), constraints=constraints)

        portfolios = np.zeros((frontier_size, n))
        returns_ = np.zeros(frontier_size)
        variances_ = np.zeros(frontier_size)

        logger.debug(f'Return space is divided in {frontier_size} samples. Calculating portfolio for each of them')

        for i, ret_i in enumerate(returns_space):
            ret_target.value = ret_i
            problem.solve(solver=cp.SCIP)

            if problem.status not in ['optimal', 'optimal_inaccurate']:
                raise OptimizationError(problem.status)

            portfolio_weights = round_to_sum_1(weights.value)

            portfolios[i] = portfolio_weights
            returns_[i] = ret.value
            variances_[i] = risk.value

        return portfolios, returns_, variances_


class StrategyOptimizer(ABC):
    def __init__(self):
        self.settings = config.get_settings()
        self.simulation_service = simulation_service.LogNormalSimulationService()

    def calculate_efficient_frontier(self,
                                     product_expected_returns: np.ndarray,
                                     product_covariance: np.ndarray,
                                     sp_efficient_frontier: pd.DataFrame,
                                     investment_horizon: int,
                                     cash_flows: np.ndarray,
                                     percentile: float,
                                     user_preferences: UserPreferences,
                                     preferred_measure: TargetMeasureEnum = TargetMeasureEnum.RISK
                                     ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Calculate the efficient frontier by finding optimal portfolios across a range of preferred measure levels.

        Args:
            product_expected_returns: Array of expected product returns for each asset
            product_covariance: Covariance matrix of asset product returns
            sp_efficient_frontier: Array of single-period efficient frontier weights
            investment_horizon: Number of periods in the investment horizon
            cash_flows: Array of cash flows for each period
            percentile: Percentile value for risk calculations
            user_preferences: User preferences for risk and return measures
            preferred_measure: Preferred measure for sampling efficient frontier

        Returns:
            Tuple containing:
                - Array of optimal strategies (portfolio indices for each period)
                - Array of expected product returns for each strategy
                - Array of risks for each strategy
        """
        logger.debug('Calculating strategy efficient frontier')

        risk_measure = RiskMeasureFactory.create_risk_measure(user_preferences.risk_measure_type)
        return_measure = ReturnMeasureFactory.create_return_measure(user_preferences.return_measure_type)
        return_calculator = ReturnCalculatorFactory.create_return_calculator(user_preferences.return_calculation_type)

        logger.debug(f'User defined: '
                     f'     risk measure: {user_preferences.risk_measure_type}'
                     f'     return measure: {user_preferences.return_measure_type}'
                     f'     return calculator: {user_preferences.return_calculation_type}')

        user_calculations = PreferenceBasedCalculations(risk_measure, return_measure, return_calculator)

        alphas = self.calculate_alphas(cash_flows)

        logger.debug('Simulating portfolio efficient frontier returns')
        sp_ef_simulated_returns = self.simulate_sp_ef_returns(sp_efficient_frontier)
        logger.debug('Calculating constant strategy efficient frontier returns and risks')
        mp_ef_returns = self.calculate_mp_ef_returns(sp_ef_simulated_returns, user_calculations, investment_horizon, alphas=alphas, percentile=percentile)
        mp_ef_risks = self.calculate_mp_ef_risks(sp_ef_simulated_returns, user_calculations, investment_horizon, alphas=alphas, percentile=percentile)

        if preferred_measure == TargetMeasureEnum.RISK:
            target_space = np.linspace(np.min(mp_ef_risks), np.max(mp_ef_risks), num=self.settings.default_strategy_frontier_size, endpoint=True)
            calculate_strategy = self.calculate_strategy_for_target_risk
        else:
            target_space = np.linspace(np.max(mp_ef_returns), np.min(mp_ef_returns), num=self.settings.default_strategy_frontier_size, endpoint=True)
            calculate_strategy = self.calculate_strategy_for_target_return

        strategies = np.empty((self.settings.default_strategy_frontier_size, investment_horizon), dtype=np.int32)
        strategy_returns = np.empty(self.settings.default_strategy_frontier_size)
        strategy_risks = np.empty(self.settings.default_strategy_frontier_size)

        logger.debug(f'Target space is divided in {self.settings.default_strategy_frontier_size} samples. Calculating strategy for each of them')

        strategy = None

        for index in range(len(target_space)):
            strategy, strategy_return, strategy_risk = calculate_strategy(product_expected_returns=product_expected_returns,
                                                                          product_covariance=product_covariance,
                                                                          sp_efficient_frontier=sp_efficient_frontier,
                                                                          target=float(target_space[index]),
                                                                          investment_horizon=investment_horizon,
                                                                          cash_flows=cash_flows,
                                                                          percentile=percentile,
                                                                          user_preferences=user_preferences,
                                                                          starting_strategy=strategy)

            strategies[index] = strategy
            strategy_returns[index] = strategy_return
            strategy_risks[index] = strategy_risk

        sorted_indices = np.argsort(strategy_risks)
        strategies = strategies[sorted_indices]
        strategy_returns = strategy_returns[sorted_indices]
        strategy_risks = strategy_risks[sorted_indices]

        return strategies, strategy_returns, strategy_risks

    @abstractmethod
    def calculate_strategy_for_target_risk(self,
                                           product_expected_returns: np.ndarray,
                                           product_covariance: np.ndarray,
                                           sp_efficient_frontier: pd.DataFrame,
                                           target: float,
                                           investment_horizon: int,
                                           cash_flows: np.ndarray,
                                           percentile: float,
                                           user_preferences: UserPreferences,
                                           starting_strategy: Optional[np.ndarray] = None
                                           ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Calculate the optimal investment strategy that maximizes return for a given risk target.

        Args:
            product_expected_returns: Array of expected product returns for each asset
            product_covariance: Covariance matrix of asset product returns
            sp_efficient_frontier: Array of single-period efficient frontier weights
            target: The target risk level to achieve
            investment_horizon: Number of periods in the investment horizon
            cash_flows: Array of cash flows for each period
            percentile: Percentile value for risk calculations
            user_preferences: User preferences for risk and return measures
            starting_strategy: Strategy from which calculation will be started

        Returns:
            Tuple containing:
                - The optimal strategy (array of portfolio indices for each period)
                - The expected return of the optimal strategy
                - The risk of the optimal strategy
        """
        pass

    @abstractmethod
    def calculate_strategy_for_target_return(self,
                                             product_expected_returns: np.ndarray,
                                             product_covariance: np.ndarray,
                                             sp_efficient_frontier: pd.DataFrame,
                                             target: float,
                                             investment_horizon: int,
                                             cash_flows: np.ndarray,
                                             percentile: float,
                                             user_preferences: UserPreferences,
                                             starting_strategy: Optional[np.ndarray] = None
                                             ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Calculate the optimal investment strategy that minimizes risk for a given return target

        Args:
            product_expected_returns: Array of expected product returns for each asset
            product_covariance: Covariance matrix of asset product returns
            sp_efficient_frontier: Array of single-period efficient frontier weights
            target: The target return level to achieve
            investment_horizon: Number of periods in the investment horizon
            cash_flows: Array of cash flows for each period
            percentile: Percentile value for risk calculations
            user_preferences: User preferences for risk and return measures
            starting_strategy: Strategy from which calculation will be started

        Returns:
            Tuple containing:
                - The optimal strategy (array of portfolio indices for each period)
                - The expected return of the optimal strategy
                - The risk of the optimal strategy
        """
        pass

    # TODO create smarter method for calculating alphas (ensure negative cash flows are possible)
    @staticmethod
    def calculate_alphas(cash_flows: np.ndarray) -> np.ndarray:
        return cash_flows / cash_flows.sum()

    def simulate_sp_ef_returns(self, sp_efficient_frontier: pd.DataFrame) -> np.ndarray:
        simulated_product_returns = self.simulation_service.get_returns_simulations_for_isins(isins=None)
        simulated_product_returns = simulated_product_returns.reindex(columns=sp_efficient_frontier.columns)
        sp_efficient_frontier_returns = simulated_product_returns @ sp_efficient_frontier.T
        return sp_efficient_frontier_returns.values

    def calculate_simulated_mp_ef_returns(self, sp_ef_returns: np.ndarray, user_calculations: PreferenceBasedCalculations, investment_horizon: int, **kwargs) -> np.ndarray:
        frontier_size = self.settings.default_portfolio_frontier_size
        all_sp_strategies = [np.repeat(i, investment_horizon) for i in range(frontier_size)]
        simulated_mp_ef_returns = np.hstack([user_calculations.return_calculator.calculate_strategy_returns(sp_ef_returns[:, sp_strategy], **kwargs) for sp_strategy in all_sp_strategies])
        return simulated_mp_ef_returns

    def calculate_mp_ef_returns(self, sp_ef_simulated_returns: np.ndarray, user_calculations: PreferenceBasedCalculations, investment_horizon: int, **kwargs) -> np.ndarray:
        simulated_mp_ef_returns = self.calculate_simulated_mp_ef_returns(sp_ef_simulated_returns, user_calculations, investment_horizon, **kwargs)
        mp_ef_risks = user_calculations.return_measure.calculate_return(simulated_mp_ef_returns)
        return mp_ef_risks

    def calculate_mp_ef_risks(self, sp_ef_simulated_returns: np.ndarray, user_calculations: PreferenceBasedCalculations, investment_horizon: int, **kwargs) -> np.ndarray:
        simulated_mp_ef_returns = self.calculate_simulated_mp_ef_returns(sp_ef_simulated_returns, user_calculations, investment_horizon, **kwargs)
        mp_ef_risks = user_calculations.risk_measure.calculate_risk(simulated_mp_ef_returns, **kwargs)
        return mp_ef_risks

    def get_min_risk_strategy_index(self, sp_ef_simulated_returns: np.ndarray, user_calculations: PreferenceBasedCalculations, investment_horizon: int, **kwargs) -> int:
        mp_ef_risks = self.calculate_mp_ef_risks(sp_ef_simulated_returns, user_calculations, investment_horizon, **kwargs)
        return np.argmin(mp_ef_risks).item()

    def get_max_risk_strategy_index(self, sp_ef_simulated_returns: np.ndarray, user_calculations: PreferenceBasedCalculations, investment_horizon: int, **kwargs) -> int:
        mp_ef_risks = self.calculate_mp_ef_risks(sp_ef_simulated_returns, user_calculations, investment_horizon, **kwargs)
        return np.argmax(mp_ef_risks).item()


class AdaptiveSinglePeriodStrategyOptimizer(StrategyOptimizer):

    def calculate_strategy_for_target_risk(self,
                                           product_expected_returns: np.ndarray,
                                           product_covariance: np.ndarray,
                                           sp_efficient_frontier: np.ndarray,
                                           target: float,
                                           investment_horizon: int,
                                           cash_flows: np.ndarray,
                                           percentile: float,
                                           user_preferences: UserPreferences,
                                           starting_strategy: Optional[np.ndarray] = None,
                                           ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        logger.debug(f'Calculating strategy for target risk: {target}')
        return self._calculate_strategy(
            product_expected_returns=product_expected_returns,
            product_covariance=product_covariance,
            sp_efficient_frontier=sp_efficient_frontier,
            target=target,
            investment_horizon=investment_horizon,
            cash_flows=cash_flows,
            percentile=percentile,
            user_preferences=user_preferences,
            calculate_strategies_under_target=lambda returns, risks, target: np.where(risks <= target)[0])

    def calculate_strategy_for_target_return(self,
                                             product_expected_returns: np.ndarray,
                                             product_covariance: np.ndarray,
                                             sp_efficient_frontier: np.ndarray,
                                             target: float,
                                             investment_horizon: int,
                                             cash_flows: np.ndarray,
                                             percentile: float,
                                             user_preferences: UserPreferences,
                                             starting_strategy: Optional[np.ndarray] = None,
                                             ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        logger.debug(f'Calculating strategy for target return: {target}')
        return self._calculate_strategy(
            product_expected_returns=product_expected_returns,
            product_covariance=product_covariance,
            sp_efficient_frontier=sp_efficient_frontier,
            target=target,
            investment_horizon=investment_horizon,
            cash_flows=cash_flows,
            percentile=percentile,
            user_preferences=user_preferences,
            calculate_strategies_under_target=lambda returns, risks, target: np.where(returns <= target)[0])

    # TODO see if this method can be deleted
    def calculate_constrained_strategy_for_target_risk(self,
                                                       product_expected_returns: np.ndarray,
                                                       product_covariance: np.ndarray,
                                                       sp_efficient_frontier: pd.DataFrame,
                                                       target_risk: float,
                                                       investment_horizon: int,
                                                       cash_flows: np.ndarray,
                                                       percentile: float,
                                                       user_preferences: UserPreferences,
                                                       min_portfolio_index: int = 0
                                                       ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:

        return_measure = ReturnMeasureFactory.create_return_measure(user_preferences.return_measure_type)
        risk_measure = RiskMeasureFactory.create_risk_measure(user_preferences.risk_measure_type)
        return_calculator = ReturnCalculatorFactory.create_return_calculator(user_preferences.return_calculation_type)

        user_calculations = PreferenceBasedCalculations(
            risk_measure=risk_measure,
            return_measure=return_measure,
            return_calculator=return_calculator,
        )

        alphas = self.calculate_alphas(cash_flows)

        sp_ef_simulated_returns = self.simulate_sp_ef_returns(sp_efficient_frontier)

        mp_ef_returns = self.calculate_mp_ef_returns(sp_ef_simulated_returns, user_calculations, investment_horizon, alphas=alphas)
        mp_ef_risks = self.calculate_mp_ef_risks(sp_ef_simulated_returns, user_calculations, investment_horizon, alphas=alphas, percentile=percentile)

        strategies_under_target_risk = np.where(mp_ef_risks <= target_risk)[0]

        if strategies_under_target_risk.size > 0:
            current_strategy_index = strategies_under_target_risk[-1]
        else:
            current_strategy_index = 0

        if current_strategy_index < min_portfolio_index:
            current_strategy_index = min_portfolio_index

        current_strategy = np.repeat(current_strategy_index, investment_horizon)
        current_strategy_return = mp_ef_returns[current_strategy_index]
        current_strategy_risk = mp_ef_risks[current_strategy_index]

        return current_strategy, current_strategy_return, current_strategy_risk

    def _calculate_strategy(self,
                            product_expected_returns: np.ndarray,
                            product_covariance: np.ndarray,
                            sp_efficient_frontier: pd.DataFrame,
                            target: float,
                            investment_horizon: int,
                            cash_flows: np.ndarray,
                            percentile: float,
                            user_preferences: UserPreferences,
                            calculate_strategies_under_target: Callable[[np.ndarray, np.ndarray, float], np.ndarray],
                            ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:

        return_measure = ReturnMeasureFactory.create_return_measure(user_preferences.return_measure_type)
        risk_measure = RiskMeasureFactory.create_risk_measure(user_preferences.risk_measure_type)
        return_calculator = ReturnCalculatorFactory.create_return_calculator(user_preferences.return_calculation_type)

        user_calculations = PreferenceBasedCalculations(
            risk_measure=risk_measure,
            return_measure=return_measure,
            return_calculator=return_calculator,
        )

        logger.debug(f'User defined: '
                     f'     risk measure: {user_preferences.risk_measure_type}'
                     f'     return measure: {user_preferences.return_measure_type}'
                     f'     return calculator: {user_preferences.return_calculation_type}')

        alphas = self.calculate_alphas(cash_flows)

        sp_ef_simulated_returns = self.simulate_sp_ef_returns(sp_efficient_frontier)

        mp_ef_returns = self.calculate_mp_ef_returns(sp_ef_simulated_returns, user_calculations, investment_horizon, alphas=alphas)
        mp_ef_risks = self.calculate_mp_ef_risks(sp_ef_simulated_returns, user_calculations, investment_horizon, alphas=alphas, percentile=percentile)

        strategies_under_target = calculate_strategies_under_target(mp_ef_returns, mp_ef_risks, target)

        if strategies_under_target.size > 0:
            current_strategy_index = strategies_under_target[-1]
        else:
            current_strategy_index = 0

        current_strategy = np.repeat(current_strategy_index, investment_horizon)
        current_strategy_return = mp_ef_returns[current_strategy_index]
        current_strategy_risk = mp_ef_risks[current_strategy_index]

        return current_strategy, current_strategy_return, current_strategy_risk


class MultiPeriodStrategyOptimizer(StrategyOptimizer):

    def calculate_strategy_for_target_return(self,
                                             product_expected_returns: np.ndarray,
                                             product_covariance: np.ndarray,
                                             sp_efficient_frontier: np.ndarray,
                                             target: float,
                                             investment_horizon: int,
                                             cash_flows: np.ndarray,
                                             percentile: float,
                                             user_preferences: UserPreferences,
                                             starting_strategy: Optional[np.ndarray] = None,
                                             ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        logger.info(f'Calculating strategy for target return: {target:.4f}')
        return self._calculate_strategy(
            product_expected_returns=product_expected_returns,
            product_covariance=product_covariance,
            sp_efficient_frontier=sp_efficient_frontier,
            target=target,
            investment_horizon=investment_horizon,
            cash_flows=cash_flows,
            percentile=percentile,
            user_preferences=user_preferences,
            create_starting_strategy=self.create_starting_strategy_for_target_return,
            calculate_feasible_strategies=self._calculate_feasible_strategies_for_target_return,
            get_best_strategy_index=lambda feasible_strategy_returns, feasible_strategy_risks: np.argmin(feasible_strategy_risks),
            starting_strategy=starting_strategy,
        )

    def calculate_strategy_for_target_risk(self,
                                           product_expected_returns: np.ndarray,
                                           product_covariance: np.ndarray,
                                           sp_efficient_frontier: np.ndarray,
                                           target: float,
                                           investment_horizon: int,
                                           cash_flows: np.ndarray,
                                           percentile: float,
                                           user_preferences: UserPreferences,
                                           starting_strategy: Optional[np.ndarray] = None,
                                           ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        logger.info(f'Calculating strategy for target risk: {target:.4f}')
        return self._calculate_strategy(
            product_expected_returns=product_expected_returns,
            product_covariance=product_covariance,
            sp_efficient_frontier=sp_efficient_frontier,
            target=target,
            investment_horizon=investment_horizon,
            cash_flows=cash_flows,
            percentile=percentile,
            user_preferences=user_preferences,
            create_starting_strategy=self.create_starting_strategy_for_target_risk,
            calculate_feasible_strategies=self._calculate_feasible_strategies_for_target_risk,
            get_best_strategy_index=lambda feasible_strategy_returns, feasible_strategy_risks: np.argmax(feasible_strategy_returns),
            starting_strategy=starting_strategy,
        )

    def _calculate_strategy(self,
                            product_expected_returns: np.ndarray,
                            product_covariance: np.ndarray,
                            sp_efficient_frontier: pd.DataFrame,
                            target: float,
                            investment_horizon: int,
                            cash_flows: np.ndarray,
                            percentile: float,
                            user_preferences: UserPreferences,
                            create_starting_strategy: Callable[[int, int, int], np.ndarray],
                            calculate_feasible_strategies: CalculateFeasibleStrategiesType,
                            get_best_strategy_index: Callable[[np.ndarray, np.ndarray], int],
                            starting_strategy: Optional[np.ndarray] = None,
                            **kwargs
                            ) -> (np.ndarray, float, float):
        """
        Core algorithm that iteratively finds optimal strategies based on the given target and constraints.

        Args:
            product_expected_returns: Array of expected product returns for each asset
            product_covariance: Covariance matrix of asset product returns
            target: Target value (risk or return) to optimize for
            investment_horizon: Number of periods in the investment horizon
            cash_flows: Array of cash flows for each period
            percentile: Percentile value for risk calculations
            user_preferences: User preferences for risk and return measures
            create_starting_strategy: Function to create the initial strategy
            calculate_feasible_strategies: Function to calculate feasible neighboring strategies
            get_best_strategy_index: Function to select the best strategy from feasible ones

        Returns:
            Tuple containing:
                - The optimal strategy (array of portfolio indices for each period)
                - The expected return of the optimal strategy
                - The risk of the optimal strategy
        """
        logger.debug(f'Calculating strategy fot target {target:.4f}, investment horizon {investment_horizon}')

        risk_measure = RiskMeasureFactory.create_risk_measure(user_preferences.risk_measure_type)
        return_measure = ReturnMeasureFactory.create_return_measure(user_preferences.return_measure_type)
        return_calculator = ReturnCalculatorFactory.create_return_calculator(user_preferences.return_calculation_type)

        user_calculations = PreferenceBasedCalculations(risk_measure, return_measure, return_calculator)

        min_portfolio_index = kwargs.get('min_portfolio_index', 0)

        alphas = self.calculate_alphas(cash_flows)
        logger.debug(f'Alphas calculated: {alphas.reshape(1, -1)}')

        sp_ef_simulated_returns = self.simulate_sp_ef_returns(sp_efficient_frontier)
        logger.debug(f'Simulated sp_ef_return shape: {sp_ef_simulated_returns.shape}')

        min_risk_portfolio_index = self.get_min_risk_strategy_index(sp_ef_simulated_returns, user_calculations, investment_horizon, alphas=alphas)
        max_risk_portfolio_index = self.get_max_risk_strategy_index(sp_ef_simulated_returns, user_calculations, investment_horizon, alphas=alphas)

        if min_portfolio_index > min_risk_portfolio_index:
            logger.warning(f'min_portfolio_index {min_portfolio_index} > min_risk_portfolio_index {min_risk_portfolio_index}, updating min_risk_portfolio_index')
            min_risk_portfolio_index = min_portfolio_index

        current_strategy = create_starting_strategy(min_risk_portfolio_index, max_risk_portfolio_index, investment_horizon) if starting_strategy is None else starting_strategy
        logger.debug(f'Initial strategy {current_strategy.reshape(1, -1)}')

        feasible_strategies, feasible_strategy_returns, feasible_strategy_risks = calculate_feasible_strategies(
            current_strategy,
            sp_ef_simulated_returns,
            target,
            investment_horizon,
            percentile,
            user_calculations,
            alphas=alphas,
            min_portfolio_index=min_portfolio_index,
        )

        while feasible_strategies.size != 0:
            best_strategy_index = get_best_strategy_index(feasible_strategy_returns, feasible_strategy_risks)
            current_strategy = feasible_strategies[best_strategy_index]

            feasible_strategies, feasible_strategy_returns, feasible_strategy_risks = calculate_feasible_strategies(
                current_strategy,
                sp_ef_simulated_returns,
                target,
                investment_horizon,
                percentile,
                user_calculations,
                alphas=alphas
            )

        current_strategy_returns = user_calculations.return_calculator.calculate_strategy_returns(
            simulated_returns=sp_ef_simulated_returns[:, current_strategy],
            alphas=alphas
        )
        current_strategy_return = user_calculations.return_measure.calculate_return(current_strategy_returns)
        current_strategy_risk = user_calculations.risk_measure.calculate_risk(current_strategy_returns, alphas=alphas)

        logger.info(f'Optimal strategy found: {current_strategy.reshape(1, -1)}')
        logger.info(f'Strategy expected return: {current_strategy_return.item():.6f}, risk: {current_strategy_risk.item():.6f}')

        return current_strategy, current_strategy_return, current_strategy_risk

    def _calculate_feasible_strategies(self,
                                       current_strategy: np.ndarray,
                                       sp_ef_simulated_returns: np.ndarray,
                                       target: float,
                                       investment_horizon: int,
                                       percentile: float,
                                       user_calculations: PreferenceBasedCalculations,
                                       move_to_next_portfolio: Callable[[int], int],
                                       is_out_of_target: Callable[[np.ndarray, np.ndarray, float], bool],
                                       **kwargs
                                       ) -> (np.ndarray, np.ndarray, np.ndarray):
        logger.debug(f'Calculating feasible strategies for current strategy: {current_strategy.reshape(1, -1)}')
        feasible_strategies = []
        feasible_strategy_returns = []
        feasible_strategy_risks = []

        # TODO check if this breaks anything
        min_portfolio_index = kwargs.get('min_portfolio_index', 0)

        for period_index in range(investment_horizon):
            if self.is_out_of_bounds(current_strategy[period_index].item(), move_to_next_portfolio, min_portfolio_index):
                logger.debug(f'Period {period_index}: Out of bounds, skipping')
                continue

            new_strategy = current_strategy.copy()
            new_strategy[period_index] = move_to_next_portfolio(current_strategy[period_index].item())

            if not self.is_sorted(new_strategy, desc=True):
                logger.debug(f'Period {period_index}: New strategy not sorted, skipping')
                continue

            new_strategy_returns = user_calculations.return_calculator.calculate_strategy_returns(sp_ef_simulated_returns[:, new_strategy], **kwargs)
            new_strategy_return = user_calculations.return_measure.calculate_return(new_strategy_returns)
            new_strategy_risk = user_calculations.risk_measure.calculate_risk(new_strategy_returns, percentile=percentile)

            if is_out_of_target(new_strategy_return, new_strategy_risk, target):
                logger.debug(f'Period {period_index}: New strategy out of target (return: {new_strategy_return.item():.6f}, risk: {new_strategy_risk.item():.6f}), skipping')
                continue

            logger.debug(f'Period {period_index}: New strategy found')

            feasible_strategies.append(new_strategy)
            feasible_strategy_returns.append(new_strategy_return)
            feasible_strategy_risks.append(new_strategy_risk)

        logger.debug(f'Total feasible strategies found: {len(feasible_strategies)}')

        return np.array(feasible_strategies), np.array(feasible_strategy_returns), np.array(feasible_strategy_risks)

    def _calculate_feasible_strategies_for_target_risk(self,
                                                       current_strategy: np.ndarray,
                                                       sp_ef_simulated_returns: np.ndarray,
                                                       target_risk: float,
                                                       investment_horizon: int,
                                                       percentile: float,
                                                       user_calculations: PreferenceBasedCalculations,
                                                       **kwargs
                                                       ) -> (np.ndarray, np.ndarray, np.ndarray):
        move_to_next_portfolio = self.move_to_riskier_portfolio
        is_out_of_target = self.is_out_of_risk_target

        return self._calculate_feasible_strategies(
            current_strategy=current_strategy,
            sp_ef_simulated_returns=sp_ef_simulated_returns,
            target=target_risk,
            investment_horizon=investment_horizon,
            percentile=percentile,
            user_calculations=user_calculations,
            move_to_next_portfolio=move_to_next_portfolio,
            is_out_of_target=is_out_of_target,
            **kwargs
        )

    def _calculate_feasible_strategies_for_target_return(self,
                                                         current_strategy: np.ndarray,
                                                         sp_ef_simulated_returns: np.ndarray,
                                                         target_return: float,
                                                         investment_horizon: int,
                                                         percentile: float,
                                                         user_calculations: PreferenceBasedCalculations,
                                                         **kwargs
                                                         ) -> (np.ndarray, np.ndarray, np.ndarray):
        move_to_next_portfolio = self.move_to_less_riskier_portfolio
        is_out_of_target = self.is_out_of_return_target

        return self._calculate_feasible_strategies(
            current_strategy=current_strategy,
            sp_ef_simulated_returns=sp_ef_simulated_returns,
            target=target_return,
            investment_horizon=investment_horizon,
            percentile=percentile,
            user_calculations=user_calculations,
            move_to_next_portfolio=move_to_next_portfolio,
            is_out_of_target=is_out_of_target,
            **kwargs
        )

    # TODO move to util
    @staticmethod
    def is_sorted(array: np.ndarray, desc=True):
        return np.all(array[:-1] >= array[1:]) if desc else np.all(array[:-1] <= array[1:])

    @staticmethod
    def approx_compare(x: float, y: float, mode: str = 'lte'):
        if mode == 'lte':
            return x <= y or np.isclose(x, y)
        elif mode == 'gte':
            return x >= y or np.isclose(x, y)
        else:
            raise ValueError("Invalid mode. Use 'lte' or 'gte'.")

    @staticmethod
    def move_to_riskier_portfolio(current_portfolio_index: int) -> int:
        return current_portfolio_index + 1

    @staticmethod
    def move_to_less_riskier_portfolio(current_portfolio_index: int) -> int:
        return current_portfolio_index - 1

    def is_out_of_bounds(self, current_portfolio_index: int, move_to_next_portfolio: Callable[[int], int], min_portfolio_index: int) -> bool:
        frontier_size = self.settings.default_portfolio_frontier_size
        return move_to_next_portfolio(current_portfolio_index) < min_portfolio_index or move_to_next_portfolio(current_portfolio_index) == frontier_size

    def is_out_of_risk_target(self, new_strategy_return, new_strategy_risk, target):
        return not self.approx_compare(new_strategy_risk, target, mode='lte')

    def is_out_of_return_target(self, new_strategy_return, new_strategy_risk, target):
        return not self.approx_compare(new_strategy_return, target, mode='gte')

    @staticmethod
    def create_starting_strategy_for_target_return(min_risk_index, max_risk_index, investment_horizon):
        return np.repeat(max_risk_index, investment_horizon)

    @staticmethod
    def create_starting_strategy_for_target_risk(min_risk_index, max_risk_index, investment_horizon):
        return np.repeat(min_risk_index, investment_horizon)
