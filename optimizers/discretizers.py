import numpy as np
import cvxpy as cp
from abc import ABC, abstractmethod
import pandas as pd
from cvxpy import Expression

from configuration import config
from errors.errors import OptimizationError
from repositories.old_mongo_repositories import etf_indices_repository, etf_repository


class Discretizer(ABC):
    """
    Abstract base class for discretizing portfolios and efficient frontiers.

    Attributes:
        settings: Configuration settings for the application.
        indices_repository: Repository for ETF indices data.
        etf_repository: Repository for ETF data.
    """
    def __init__(self):
        self.settings = config.get_settings()
        self.indices_repository = etf_indices_repository.EtfIndicesRepository()
        self.etf_repository = etf_repository.EtfRepository()

    @abstractmethod
    def discretize_portfolio(self,
                             weights: np.ndarray,
                             prices: np.ndarray,
                             investment_amount: float,
                             covariance: np.ndarray
                            ) -> np.ndarray:
        """
        Discretize a portfolio by converting fractional weights to integer quantities.

        Args:
            weights (np.ndarray): Target portfolio weights.
            prices (np.ndarray): Current prices of assets.
            investment_amount (float): Total investment amount.
            covariance (np.ndarray): Covariance matrix of asset returns.

        Returns:
            np.ndarray: Discretized portfolio quantities.
        """
        pass

    @abstractmethod
    def discretize_efficient_frontier(self,
                                      efficient_frontier: np.ndarray,
                                      prices_df: pd.DataFrame,
                                      investment_amount: float,
                                      covariance: np.ndarray
                                      ) -> pd.DataFrame:
        """
        Discretize an efficient frontier into integer portfolios.

        Args:
            efficient_frontier (np.ndarray): Array of portfolio weights on the efficient frontier.
            prices_df (pd.DataFrame): DataFrame containing asset prices.
            investment_amount (float): Total investment amount.
            covariance (np.ndarray): Covariance matrix of asset returns.

        Returns:
            pd.DataFrame: DataFrame of discretized portfolios.
        """
        pass

    @staticmethod
    def _calculate_tracking_error(weights: np.ndarray,
                                  quantities: np.ndarray,
                                  covariance: np.ndarray,
                                  prices: np.ndarray,
                                  investment_amount: float,
                                  optimization: bool = False
                                  ) -> float | Expression:
        """
        Calculate the tracking error between target weights and discretized weights.

        Args:
            weights (np.ndarray): Target portfolio weights.
            quantities (np.ndarray): Portfolio quantities.
            covariance (np.ndarray): Covariance matrix of asset returns.
            prices (np.ndarray): Asset prices.
            investment_amount (float): Total investment amount.
            optimization (bool): Whether the calculation is for optimization context (using CVXPY).

        Returns:
            float | Expression: Tracking error as a float or CVXPY expression.
        """
        new_weights = cp.multiply(quantities, prices) / investment_amount if optimization else np.multiply(quantities, prices) / investment_amount

        delta_weights = weights - new_weights

        return delta_weights.T @ covariance @ delta_weights


class TrackingErrorDiscretizer(Discretizer):
    """
    Discretizer that minimizes tracking error when converting portfolio weights to integer quantities.
    """
    def __init__(self):
        super(TrackingErrorDiscretizer, self).__init__()

    def discretize_portfolio(self,
                             weights: np.ndarray,
                             prices: np.ndarray,
                             investment_amount: float,
                             covariance: np.ndarray
                             ) -> np.ndarray:
        """
        Discretize portfolio weights into integer quantities while minimizing tracking error.

        Args:
            weights (np.ndarray): Target portfolio weights.
            prices (np.ndarray): Asset prices.
            investment_amount (float): Total investment amount.
            covariance (np.ndarray): Covariance matrix of asset returns.

        Returns:
            np.ndarray: Integer portfolio quantities.
        """
        weights = weights.reshape((-1, 1))

        quantities = np.floor(investment_amount * weights / prices)

        tracking_error = self._calculate_tracking_error(weights, quantities, covariance, prices, investment_amount)

        feasible_portfolios, deltas = self._calculate_feasible_discrete_portfolios(weights, quantities, covariance, prices, tracking_error, investment_amount)

        while feasible_portfolios.shape != (0,):
            index_of_min_delta = np.argmin(deltas)
            quantities = feasible_portfolios[index_of_min_delta].reshape(-1, 1)
            feasible_portfolios, deltas = self._calculate_feasible_discrete_portfolios(weights, quantities, covariance, prices, tracking_error, investment_amount)

        return quantities.T.squeeze()

    def discretize_efficient_frontier(self,
                                      efficient_frontier: np.ndarray,
                                      prices_df: pd.DataFrame,
                                      investment_amount: float,
                                      covariance: np.ndarray
                                      ) -> pd.DataFrame:
        """
        Discretize an efficient frontier into integer portfolios.

        Args:
            efficient_frontier (np.ndarray): Array of portfolio weights on the efficient frontier.
            prices_df (pd.DataFrame): DataFrame containing asset prices.
            investment_amount (float): Total investment amount.
            covariance (np.ndarray): Covariance matrix of asset returns.

        Returns:
            pd.DataFrame: DataFrame of discretized portfolios.
        """
        prices = prices_df.values.reshape((-1, 1))
        discrete_efficient_frontier = np.stack([self.discretize_portfolio(portfolio, prices, investment_amount, covariance) for portfolio in efficient_frontier])
        return pd.DataFrame(discrete_efficient_frontier, columns=prices_df.columns, dtype=int)

    def _calculate_feasible_discrete_portfolios(self,
                                                weights: np.ndarray,
                                                quantities: np.ndarray,
                                                covariance: np.ndarray,
                                                prices: np.ndarray,
                                                tracking_error: float,
                                                investment_amount: float
                                                ) -> (np.ndarray, np.ndarray):
        """
        Calculate feasible discrete portfolios and their tracking error deltas.

        Args:
            weights (np.ndarray): Target portfolio weights.
            quantities (np.ndarray): Current portfolio quantities.
            covariance (np.ndarray): Covariance matrix of asset returns.
            prices (np.ndarray): Asset prices.
            tracking_error (float): Current tracking error.
            investment_amount (float): Total investment amount.

        Returns:
            tuple[np.ndarray, np.ndarray]: Feasible portfolios and their tracking error deltas.
        """

        feasible_portfolios = []
        deltas = []

        for i in range(len(quantities)):
            new_quantities = quantities.copy()

            new_quantities[i] += 1

            delta_tracking_error = self._calculate_tracking_error(weights, new_quantities, covariance, prices, investment_amount) - tracking_error

            if np.dot(new_quantities.T, prices) <= investment_amount and delta_tracking_error < 0:
                feasible_portfolios.append(new_quantities)
                deltas.append(delta_tracking_error)

        feasible_portfolios, deltas = np.array(feasible_portfolios), np.array(deltas)

        return feasible_portfolios, deltas


class TrackingErrorQPDiscretizer(Discretizer):
    """
    Discretizer that uses quadratic programming (QP) to minimize tracking error when converting portfolio weights to integer quantities.

    This class utilizes the CVXPY library to solve an integer optimization problem that aligns discretized portfolio weights with target
    weights while adhering to investment constraints.
    """
    def __init__(self):
        super(TrackingErrorQPDiscretizer, self).__init__()

    def discretize_portfolio(self,
                             weights: np.ndarray,
                             prices: np.ndarray,
                             investment_amount: float,
                             covariance: np.ndarray
                             ) -> np.ndarray:
        """
        Discretize portfolio weights into integer quantities using quadratic programming.

        Args:
            weights (np.ndarray): Target portfolio weights (shape: Nx1).
            prices (np.ndarray): Asset prices (shape: Nx1).
            investment_amount (float): Total investment amount.
            covariance (np.ndarray): Covariance matrix of asset returns (shape: NxN).

        Returns:
            np.ndarray: Integer portfolio quantities (shape: Nx1).
        """
        weights = weights.reshape((-1, 1))

        quantities = cp.Variable(weights.shape, integer=True)

        tracking_error = self._calculate_tracking_error(weights, quantities.value, covariance, prices, investment_amount, optimization=True)

        constraints = [
            quantities >= 0,
            quantities.T @ prices <= investment_amount,
            cp.multiply(quantities, prices) <= 0.5 * prices.T @ quantities
        ]

        problem = cp.Problem(cp.Minimize(tracking_error), constraints)
        problem.solve(solver=cp.SCIP)

        if problem.status not in ['optimal', 'optimal_inaccurate']:
            raise OptimizationError(problem.status)

        return quantities.value.squeeze().astype(int)

    def discretize_efficient_frontier(self,
                                      efficient_frontier: np.ndarray,
                                      prices_df: pd.DataFrame,
                                      investment_amount: float,
                                      covariance: np.ndarray
                                      ) -> pd.DataFrame:
        """
        Discretize a series of portfolios on the efficient frontier into integer quantities.

        Args:
            efficient_frontier (np.ndarray): Array of portfolio weights on the efficient frontier (shape: MxN).
            prices_df (pd.DataFrame): DataFrame of asset prices (shape: Nx1).
            investment_amount (float): Total investment amount.
            covariance (np.ndarray): Covariance matrix of asset returns (shape: NxN).

        Returns:
            pd.DataFrame: DataFrame of discretized portfolios with integer quantities (shape: MxN).
        """
        products_returns_df, _ = self.etf_repository.get_returns_in_time_horizon(self.settings.time_horizon)
        prices = prices_df.values.reshape((-1, 1))
        discrete_efficient_frontier = np.stack([self.discretize_portfolio(portfolio, prices, investment_amount, covariance) for portfolio in efficient_frontier])
        return pd.DataFrame(discrete_efficient_frontier, columns=prices_df.columns, dtype=int)
