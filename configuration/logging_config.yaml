version: 1
disable_existing_loggers: false
formatters:
  standard:
    format: "%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s"
  json:
    class: configuration.logging_config.JsonFormatter
handlers:
  console:
    class: logging.StreamHandler
    level: "{LOG_LEVEL}"
    formatter: standard
    stream: ext://sys.stdout
  file:
    class: logging.handlers.RotatingFileHandler
    level: "{LOG_LEVEL}"
    formatter: json
    filename: "{LOG_DIR}/{LOG_FILE}"
    maxBytes: 10485760
    backupCount: 10
  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: json
    filename: "{LOG_DIR}/{ERROR_LOG_FILE}"
    maxBytes: 10485760
    backupCount: 10
loggers:
  "":
    handlers: [console, file, error_file]
    level: "{LOG_LEVEL}"
    propagate: true
