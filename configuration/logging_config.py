import logging
import os
import json
import yaml
from datetime import datetime, timezone
import logging.config

from configuration import config

settings = config.get_settings()

# Ensure log directory exists
if not os.path.exists(settings.LOG_DIR):
    os.makedirs(settings.LOG_DIR)

ENVIRONMENT = os.environ.get('ENVIRONMENT', "dev")
LOG_LEVEL = logging.DEBUG if ENVIRONMENT == 'dev' else logging.INFO

class JsonFormatter(logging.Formatter):
    """
    Formatter that outputs JSON strings after parsing the log record.
    """

    def format(self, record: logging.LogRecord) -> str:
        log_record = {
            "timestamp": datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add exception info if available
        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)

        # Add extra fields from the record
        if hasattr(record, "extra"):
            log_record.update(record.extra)

        return json.dumps(log_record)


def setup_logging() -> logging.Logger:
    """
    Set up the logging configuration from the YAML file with environment variable interpolation.

    Returns:
        The configured root logger
    """
    # Prepare environment variables for interpolation
    env_vars = {
        'LOG_LEVEL': logging.getLevelName(LOG_LEVEL),
        'LOG_DIR': settings.LOG_DIR,
        'LOG_FILE': settings.LOG_FILE,
        'ERROR_LOG_FILE': settings.ERROR_LOG_FILE
    }

    # Load and interpolate the YAML configuration
    with open('./configuration/logging_config.yaml', 'r') as f:
        config_template = f.read()
        # Replace placeholders with actual values
        config_str = config_template.format(**env_vars)
        # Parse the YAML
        config_dict = yaml.safe_load(config_str)

    config_dict['formatters']['json'] = {'()': JsonFormatter}

    # Apply the configuration
    logging.config.dictConfig(config_dict)

    # Get the root logger
    logger = logging.getLogger()

    # Log startup information
    logger.info(f"Logging configured with level: {logging.getLevelName(LOG_LEVEL)}")
    logger.info(f"Environment: {ENVIRONMENT}")

    return logger


# Initialize the root logger
root_logger = setup_logging()


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.

    Args:
        name: Usually __name__ of the module

    Returns:
        A logger configured with the project's logging settings
    """
    logger = logging.getLogger(name)
    return logger
