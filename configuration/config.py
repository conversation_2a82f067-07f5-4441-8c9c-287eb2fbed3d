from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import lru_cache


class Settings(BaseSettings):
    db_url: str = 'mongodb+srv://valusense:<EMAIL>/'
    db_name: str = 'dev'
    metadata_collection: str = 'Metadata'
    market_caps_collection: str = 'Asset_class_market_caps'
    asset_class_returns_collection: str = 'Asset_class_returns'
    etf_prices_collection_eur: str = 'ETF_prices_EUR'
    etf_prices_collection_original: str = 'ETF_prices_original'
    etf_returns_collection: str = 'ETF_returns'
    etf_simulations_collection: str = 'ETF_simulated_returns'
    etf_expected_returns_collection: str = 'ETF_expected_returns'
    etf_covariance_collection: str = 'ETF_covariance'
    etf_ter_collection: str = 'ETF_TER'
    fx_rates_collection: str = 'FX_rates'
    risk_free_rates_collection: str = 'Risk_free_rates'
    index_market_caps_collection: str = 'Index_market_caps'
    indices_returns_collection: str = 'Indices_returns'

    time_horizon: int = 3
    default_risk_percentile: float = 10
    default_n_simulations: int = 50000
    default_portfolio_frontier_size: int = 25
    default_strategy_frontier_size: int = 10

    #TODO REMOVE THIS!!
    analytics_path: str = './analytics/'

    LOG_DIR: str = './logs/'
    LOG_FILE: str = 'app.log'
    ERROR_LOG_FILE: str = 'error.log'
    LOG_FORMAT: str = "%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s"

    ENVIRONMENT: str = 'prod'

    # Type of repository to use (mongo or postgres)
    repository_type: str = 'postgres'

    # Database connection pool settings
    db_pool_min_connections: int = 2
    db_pool_max_connections: int = 20
    db_connection_timeout: int = 30

    model_config = SettingsConfigDict(env_file=".env")


@lru_cache()
def get_settings() -> Settings:
    return Settings()
