import pytz
import requests
import pandas as pd
from pymongo import MongoClient
import json
from datetime import datetime

# List of tickers
# tickers = ['XD9U.XETRA', 'IMAE.AS', 'LCUJ.XETRA', 'XMME.XETRA', 'IUSN.XETRA', 'IBTA.LSE',
#            'CSBGU0.SW', 'LQDA.LSE', 'IHYA.LSE', 'XGLE.LSE', '4GLD.XETRA', 'EPRA.PA']

tickers = ['GAGG.LSE', 'VWCE.XETRA', '4GLD.XETRA', 'EPRA.PA']

# isins = ['IE00BJ0KDR00', 'IE00B4K48X80', 'LU1781541252', 'IE00BTJRMP35', 'IE00BF4RFH31', 'IE00BYXPSP02',
#          'IE00B3VWN518', 'IE00BYXYYJ35', 'IE00BYXYYL56', 'LU0290355717', 'DE000A0S9GB0', 'LU1437018838']

asset_classes = ['Fixed Income', 'Equity', 'Commodity', 'Real Estate']

# API token (use your actual API token)
api_token = '67052a6bbe4108.46809416'

# Dictionary to hold data for DataFrame
data_dict = {}

pd.set_option('display.max_rows', None)

# Iterate over the list of tickers
for ticker in tickers:
    # Construct the URL for the current ticker
    url = f'https://eodhd.com/api/eod/{ticker}?api_token={api_token}&fmt=json&from=2019-07-25'

    # Fetch the data
    response = requests.get(url)

    # Check if the request was successful
    if response.status_code == 200:
        data = response.json()  # Parse the JSON response

        # Extract date and close price
        ticker_data = {}

        for product in data:
            if product['adjusted_close'] != 'NA' and product['adjusted_close'] != '':
                ticker_data[product['date']] = product['adjusted_close']
            elif product['previousClose'] != 'NA' and product['previousClose'] != '':
                ticker_data[product['date']] = product['previousClose']
            else:
                ticker_data[product['date']] = 0

        # Add the extracted data to the dictionary under the ticker's name
        data_dict[ticker] = ticker_data
    else:
        print(f'Failed to retrieve data for {ticker}, Status code: {response.status_code}')


# Convert dictionary to DataFrame
etf_historical_prices_df = pd.DataFrame(data_dict)

#print(etf_historical_prices_df.iloc[130:160])

# Sort DataFrame by date (index)
etf_historical_prices_df.index = pd.to_datetime(etf_historical_prices_df.index)  # Convert index to datetime format
etf_historical_prices_df = etf_historical_prices_df.sort_index()

etf_historical_prices_df.columns = asset_classes

etf_historical_prices_df.fillna(method='ffill', inplace=True)

# print(etf_historical_prices_df)

#print(etf_historical_prices_df.iloc[130:160])

with MongoClient('mongodb+srv://valusense:<EMAIL>/') as client:
    db = client['dev']
    etf_prices_original_collection = db['Asset_classes_prices_original']

    for date, row in etf_historical_prices_df.iterrows():
        mongo_record = {
            'date': date.to_pydatetime().replace(tzinfo=pytz.UTC),  # Store date as a string in the desired format (e.g., YYYY-MM-DD)
        }

        # Add ISINs and their corresponding return values to the record
        for asset_class in asset_classes:
            mongo_record[asset_class] = row[asset_class]

        #print(mongo_record)

        # Insert the record into MongoDB
        etf_prices_original_collection.insert_one(mongo_record)