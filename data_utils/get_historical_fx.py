from datetime import datetime
import json

import pytz
from pymongo import MongoClient

fx_usd = {}
fx_gbp = {}
fx_chf = {}

with open('FX_historical_rates_USD.txt', 'r') as file:
    # Load the file content as a JSON object
    data = json.load(file)

# Extract the required data
for entry in data["results"]:
    # Convert the timestamp to a readable date
    date = datetime.fromtimestamp(entry["t"] / 1000, tz=pytz.UTC)
    date = date.replace(hour=0, minute=0, second=0)

    # Add the date and closing value to the dictionary
    fx_usd[date] = entry["c"]


with open('FX_historical_rates_GBP.txt', 'r') as file:
    # Load the file content as a JSON object
    data = json.load(file)

# Extract the required data
for entry in data["results"]:
    # Convert the timestamp to a readable date
    #timestamp = entry["t"] / 1000  # Convert from milliseconds to seconds
    #date = datetime.datetime.utcfromtimestamp(timestamp).strftime('%Y-%m-%d')
    date = datetime.fromtimestamp(entry["t"] / 1000, tz=pytz.UTC)
    date = date.replace(hour=0, minute=0, second=0)

    # Add the date and closing value to the dictionary
    fx_gbp[date] = entry["c"]

with open('FX_historical_rates_CHF.txt', 'r') as file:
    # Load the file content as a JSON object
    data = json.load(file)

# Extract the required data
for entry in data["results"]:
    # Convert the timestamp to a readable date
    date = datetime.fromtimestamp(entry["t"] / 1000, tz=pytz.UTC)
    date = date.replace(hour=0, minute=0, second=0)

    # Add the date and closing value to the dictionary
    fx_chf[date] = entry["c"]

results = []

for key, value in fx_usd.items():
    fx = {'EUR': {}}
    fx['EUR']['USD'] = value
    fx['EUR']['GBP'] = fx_gbp[key]
    fx['EUR']['CHF'] = fx_chf[key]

    fx['Date'] = key

    results.append(fx)

with MongoClient('mongodb+srv://valusense:<EMAIL>/') as client:
    db = client['dev']
    fx_collection = db['FX_rates']

    fx_collection.insert_many(results)
