import pytz
import requests
import pandas as pd
from pymongo import MongoClient
import json
from datetime import datetime

response = requests.get('https://api.estr.dev/historical')

data = response.json()

with MongoClient('mongodb+srv://valusense:<EMAIL>/') as client:
    db = client['dev']
    euro_str = db['Risk_free_rates']

    # docs = list(euro_str.find().sort('date', 1))
    #
    # # Create a new collection (or you can drop and reuse the existing one)
    # euro_str.drop()  # Optionally drop if already exists
    #
    # new = db['Risk_free_rates_new']
    # # Insert documents in the new order
    # new.insert_many(docs)

    for d in data:
        date = datetime.strptime(d['date'], '%Y-%m-%d')
        if date > datetime(2019, 2, 21, 0, 0):
            save_str = {'date': date, 'rate': round(d['value']/100, 4)}
            euro_str.insert_one(save_str)

