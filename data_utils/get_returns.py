import pandas as pd
import pytz
import requests
from datetime import datetime
import sys
import os
import json
import numpy as np
from cvxpy.atoms.affine.index import index

from pymongo import MongoClient

# tickers = ['XD9U.XETRA', 'IMAE.AS', 'LCUJ.XETRA', 'XMME.XETRA', 'IUSN.XETRA', 'IBTA.LSE',
#            'CSBGU0.SW', 'LQDA.LSE', 'IHYA.LSE', 'XGLE.LSE', '4GLD.XETRA', 'EPRA.PA']
# isins = ['IE00BJ0KDR00', 'IE00B4K48X80', 'LU1781541252', 'IE00BTJRMP35', 'IE00BF4RFH31', 'IE00BYXPSP02',
#          'IE00B3VWN518', 'IE00BYXYYJ35', 'IE00BYXYYL56', 'LU0290355717', 'DE000A0S9GB0', 'LU1437018838']

asset_classes = ['Fixed Income', 'Equity', 'Commodity', 'Real Estate']

with MongoClient('mongodb+srv://valusense:<EMAIL>/') as client:
    db = client['dev']
    etf_collection_eur = db['Asset_classes_prices_EUR']

    prices = etf_collection_eur.find({}, {'_id': False})

    prices_df = pd.DataFrame(prices)
    prices_df.set_index('date', inplace=True)

    etf_historical_returns_df = prices_df.pct_change().dropna()

    etf_returns_new_collection = db['Asset_classes_returns']

    for date, row in etf_historical_returns_df.iterrows():
        mongo_record = {
            'date': date.to_pydatetime().replace(tzinfo=pytz.UTC),
        }

        # Add ISINs and their corresponding return values to the record
        for asset_class in asset_classes:
            mongo_record[asset_class] = row[asset_class]

        # Insert the record into MongoDB
        etf_returns_new_collection.insert_one(mongo_record)
