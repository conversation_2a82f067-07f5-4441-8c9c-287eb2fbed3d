from pymongo import MongoClient

client = MongoClient('mongodb+srv://valusense:<EMAIL>/')  # Replace with your URI
db = client["prod"]
collection = db["Asset_classes_market_caps"]

collection.update_many(
    {"Date": {"$exists": True}},
    {"$rename": {"Date": "date"}}
)

collection.update_many(
    {"Fixed income": {"$exists": True}},
    {"$rename": {"Fixed income": "Fixed Income"}}
)

collection.update_many(
    {"Equiy": {"$exists": True}},
    {"$rename": {"Equiy": "Equity"}}
)

collection.update_many(
    {"Commoity": {"$exists": True}},
    {"$rename": {"Commoity": "Commodity"}}
)

collection.update_many(
    {"Real estate": {"$exists": True}},
    {"$rename": {"Real estate": "Real Estate"}}
)