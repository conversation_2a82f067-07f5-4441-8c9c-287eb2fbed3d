"DATE","TIME PERIOD",PRE-ESTR
"2017-03-15","15 Mar 2017","-0.444"
"2017-03-16","16 Mar 2017","-0.443"
"2017-03-17","17 Mar 2017","-0.444"
"2017-03-20","20 Mar 2017","-0.445"
"2017-03-21","21 Mar 2017","-0.444"
"2017-03-22","22 Mar 2017","-0.444"
"2017-03-23","23 Mar 2017","-0.442"
"2017-03-24","24 Mar 2017","-0.440"
"2017-03-27","27 Mar 2017","-0.438"
"2017-03-28","28 Mar 2017","-0.442"
"2017-03-29","29 Mar 2017","-0.446"
"2017-03-30","30 Mar 2017","-0.447"
"2017-03-31","31 Mar 2017","-0.448"
"2017-04-03","03 Apr 2017","-0.447"
"2017-04-04","04 Apr 2017","-0.447"
"2017-04-05","05 Apr 2017","-0.447"
"2017-04-06","06 Apr 2017","-0.446"
"2017-04-07","07 Apr 2017","-0.447"
"2017-04-10","10 Apr 2017","-0.448"
"2017-04-11","11 Apr 2017","-0.447"
"2017-04-12","12 Apr 2017","-0.448"
"2017-04-13","13 Apr 2017","-0.448"
"2017-04-18","18 Apr 2017","-0.447"
"2017-04-19","19 Apr 2017","-0.446"
"2017-04-20","20 Apr 2017","-0.449"
"2017-04-21","21 Apr 2017","-0.448"
"2017-04-24","24 Apr 2017","-0.446"
"2017-04-25","25 Apr 2017","-0.447"
"2017-04-26","26 Apr 2017","-0.446"
"2017-04-27","27 Apr 2017","-0.447"
"2017-04-28","28 Apr 2017","-0.449"
"2017-05-02","02 May 2017","-0.443"
"2017-05-03","03 May 2017","-0.445"
"2017-05-04","04 May 2017","-0.445"
"2017-05-05","05 May 2017","-0.447"
"2017-05-08","08 May 2017","-0.445"
"2017-05-09","09 May 2017","-0.445"
"2017-05-10","10 May 2017","-0.445"
"2017-05-11","11 May 2017","-0.445"
"2017-05-12","12 May 2017","-0.446"
"2017-05-15","15 May 2017","-0.446"
"2017-05-16","16 May 2017","-0.426"
"2017-05-17","17 May 2017","-0.447"
"2017-05-18","18 May 2017","-0.447"
"2017-05-19","19 May 2017","-0.447"
"2017-05-22","22 May 2017","-0.447"
"2017-05-23","23 May 2017","-0.447"
"2017-05-24","24 May 2017","-0.450"
"2017-05-25","25 May 2017","-0.449"
"2017-05-26","26 May 2017","-0.443"
"2017-05-29","29 May 2017","-0.444"
"2017-05-30","30 May 2017","-0.446"
"2017-05-31","31 May 2017","-0.450"
"2017-06-01","01 Jun 2017","-0.447"
"2017-06-02","02 Jun 2017","-0.446"
"2017-06-05","05 Jun 2017","-0.448"
"2017-06-06","06 Jun 2017","-0.447"
"2017-06-07","07 Jun 2017","-0.439"
"2017-06-08","08 Jun 2017","-0.444"
"2017-06-09","09 Jun 2017","-0.445"
"2017-06-12","12 Jun 2017","-0.445"
"2017-06-13","13 Jun 2017","-0.443"
"2017-06-14","14 Jun 2017","-0.446"
"2017-06-15","15 Jun 2017","-0.447"
"2017-06-16","16 Jun 2017","-0.445"
"2017-06-19","19 Jun 2017","-0.445"
"2017-06-20","20 Jun 2017","-0.449"
"2017-06-21","21 Jun 2017","-0.448"
"2017-06-22","22 Jun 2017","-0.447"
"2017-06-23","23 Jun 2017","-0.446"
"2017-06-26","26 Jun 2017","-0.447"
"2017-06-27","27 Jun 2017","-0.447"
"2017-06-28","28 Jun 2017","-0.447"
"2017-06-29","29 Jun 2017","-0.447"
"2017-06-30","30 Jun 2017","-0.446"
"2017-07-03","03 Jul 2017","-0.447"
"2017-07-04","04 Jul 2017","-0.448"
"2017-07-05","05 Jul 2017","-0.447"
"2017-07-06","06 Jul 2017","-0.446"
"2017-07-07","07 Jul 2017","-0.445"
"2017-07-10","10 Jul 2017","-0.446"
"2017-07-11","11 Jul 2017","-0.447"
"2017-07-12","12 Jul 2017","-0.446"
"2017-07-13","13 Jul 2017","-0.446"
"2017-07-14","14 Jul 2017","-0.447"
"2017-07-17","17 Jul 2017","-0.447"
"2017-07-18","18 Jul 2017","-0.448"
"2017-07-19","19 Jul 2017","-0.448"
"2017-07-20","20 Jul 2017","-0.447"
"2017-07-21","21 Jul 2017","-0.447"
"2017-07-24","24 Jul 2017","-0.447"
"2017-07-25","25 Jul 2017","-0.447"
"2017-07-26","26 Jul 2017","-0.446"
"2017-07-27","27 Jul 2017","-0.446"
"2017-07-28","28 Jul 2017","-0.446"
"2017-07-31","31 Jul 2017","-0.451"
"2017-08-01","01 Aug 2017","-0.446"
"2017-08-02","02 Aug 2017","-0.447"
"2017-08-03","03 Aug 2017","-0.447"
"2017-08-04","04 Aug 2017","-0.447"
"2017-08-07","07 Aug 2017","-0.446"
"2017-08-08","08 Aug 2017","-0.446"
"2017-08-09","09 Aug 2017","-0.446"
"2017-08-10","10 Aug 2017","-0.446"
"2017-08-11","11 Aug 2017","-0.446"
"2017-08-14","14 Aug 2017","-0.445"
"2017-08-15","15 Aug 2017","-0.447"
"2017-08-16","16 Aug 2017","-0.446"
"2017-08-17","17 Aug 2017","-0.446"
"2017-08-18","18 Aug 2017","-0.445"
"2017-08-21","21 Aug 2017","-0.446"
"2017-08-22","22 Aug 2017","-0.445"
"2017-08-23","23 Aug 2017","-0.445"
"2017-08-24","24 Aug 2017","-0.447"
"2017-08-25","25 Aug 2017","-0.444"
"2017-08-28","28 Aug 2017","-0.444"
"2017-08-29","29 Aug 2017","-0.445"
"2017-08-30","30 Aug 2017","-0.445"
"2017-08-31","31 Aug 2017","-0.449"
"2017-09-01","01 Sep 2017","-0.446"
"2017-09-04","04 Sep 2017","-0.446"
"2017-09-05","05 Sep 2017","-0.447"
"2017-09-06","06 Sep 2017","-0.447"
"2017-09-07","07 Sep 2017","-0.447"
"2017-09-08","08 Sep 2017","-0.446"
"2017-09-11","11 Sep 2017","-0.447"
"2017-09-12","12 Sep 2017","-0.446"
"2017-09-13","13 Sep 2017","-0.445"
"2017-09-14","14 Sep 2017","-0.445"
"2017-09-15","15 Sep 2017","-0.445"
"2017-09-18","18 Sep 2017","-0.444"
"2017-09-19","19 Sep 2017","-0.445"
"2017-09-20","20 Sep 2017","-0.447"
"2017-09-21","21 Sep 2017","-0.427"
"2017-09-22","22 Sep 2017","-0.448"
"2017-09-25","25 Sep 2017","-0.445"
"2017-09-26","26 Sep 2017","-0.446"
"2017-09-27","27 Sep 2017","-0.445"
"2017-09-28","28 Sep 2017","-0.446"
"2017-09-29","29 Sep 2017","-0.460"
"2017-10-02","02 Oct 2017","-0.445"
"2017-10-03","03 Oct 2017","-0.446"
"2017-10-04","04 Oct 2017","-0.445"
"2017-10-05","05 Oct 2017","-0.446"
"2017-10-06","06 Oct 2017","-0.446"
"2017-10-09","09 Oct 2017","-0.446"
"2017-10-10","10 Oct 2017","-0.447"
"2017-10-11","11 Oct 2017","-0.447"
"2017-10-12","12 Oct 2017","-0.447"
"2017-10-13","13 Oct 2017","-0.447"
"2017-10-16","16 Oct 2017","-0.448"
"2017-10-17","17 Oct 2017","-0.449"
"2017-10-18","18 Oct 2017","-0.448"
"2017-10-19","19 Oct 2017","-0.449"
"2017-10-20","20 Oct 2017","-0.448"
"2017-10-23","23 Oct 2017","-0.447"
"2017-10-24","24 Oct 2017","-0.446"
"2017-10-25","25 Oct 2017","-0.447"
"2017-10-26","26 Oct 2017","-0.448"
"2017-10-27","27 Oct 2017","-0.448"
"2017-10-30","30 Oct 2017","-0.449"
"2017-10-31","31 Oct 2017","-0.465"
"2017-11-01","01 Nov 2017","-0.451"
"2017-11-02","02 Nov 2017","-0.450"
"2017-11-03","03 Nov 2017","-0.448"
"2017-11-06","06 Nov 2017","-0.449"
"2017-11-07","07 Nov 2017","-0.449"
"2017-11-08","08 Nov 2017","-0.449"
"2017-11-09","09 Nov 2017","-0.449"
"2017-11-10","10 Nov 2017","-0.448"
"2017-11-13","13 Nov 2017","-0.449"
"2017-11-14","14 Nov 2017","-0.447"
"2017-11-15","15 Nov 2017","-0.449"
"2017-11-16","16 Nov 2017","-0.450"
"2017-11-17","17 Nov 2017","-0.451"
"2017-11-20","20 Nov 2017","-0.450"
"2017-11-21","21 Nov 2017","-0.450"
"2017-11-22","22 Nov 2017","-0.450"
"2017-11-23","23 Nov 2017","-0.450"
"2017-11-24","24 Nov 2017","-0.450"
"2017-11-27","27 Nov 2017","-0.448"
"2017-11-28","28 Nov 2017","-0.450"
"2017-11-29","29 Nov 2017","-0.449"
"2017-11-30","30 Nov 2017","-0.456"
"2017-12-01","01 Dec 2017","-0.450"
"2017-12-04","04 Dec 2017","-0.450"
"2017-12-05","05 Dec 2017","-0.450"
"2017-12-06","06 Dec 2017","-0.450"
"2017-12-07","07 Dec 2017","-0.449"
"2017-12-08","08 Dec 2017","-0.449"
"2017-12-11","11 Dec 2017","-0.449"
"2017-12-12","12 Dec 2017","-0.449"
"2017-12-13","13 Dec 2017","-0.449"
"2017-12-14","14 Dec 2017","-0.449"
"2017-12-15","15 Dec 2017","-0.448"
"2017-12-18","18 Dec 2017","-0.449"
"2017-12-19","19 Dec 2017","-0.449"
"2017-12-20","20 Dec 2017","-0.448"
"2017-12-21","21 Dec 2017","-0.448"
"2017-12-22","22 Dec 2017","-0.451"
"2017-12-27","27 Dec 2017","-0.449"
"2017-12-28","28 Dec 2017","-0.448"
"2017-12-29","29 Dec 2017","-0.474"
"2018-01-02","02 Jan 2018","-0.450"
"2018-01-03","03 Jan 2018","-0.449"
"2018-01-04","04 Jan 2018","-0.450"
"2018-01-05","05 Jan 2018","-0.453"
"2018-01-08","08 Jan 2018","-0.451"
"2018-01-09","09 Jan 2018","-0.452"
"2018-01-10","10 Jan 2018","-0.452"
"2018-01-11","11 Jan 2018","-0.451"
"2018-01-12","12 Jan 2018","-0.453"
"2018-01-15","15 Jan 2018","-0.456"
"2018-01-16","16 Jan 2018","-0.454"
"2018-01-17","17 Jan 2018","-0.454"
"2018-01-18","18 Jan 2018","-0.452"
"2018-01-19","19 Jan 2018","-0.455"
"2018-01-22","22 Jan 2018","-0.453"
"2018-01-23","23 Jan 2018","-0.453"
"2018-01-24","24 Jan 2018","-0.451"
"2018-01-25","25 Jan 2018","-0.452"
"2018-01-26","26 Jan 2018","-0.453"
"2018-01-29","29 Jan 2018","-0.453"
"2018-01-30","30 Jan 2018","-0.452"
"2018-01-31","31 Jan 2018","-0.452"
"2018-02-01","01 Feb 2018","-0.455"
"2018-02-02","02 Feb 2018","-0.454"
"2018-02-05","05 Feb 2018","-0.452"
"2018-02-06","06 Feb 2018","-0.451"
"2018-02-07","07 Feb 2018","-0.452"
"2018-02-08","08 Feb 2018","-0.452"
"2018-02-09","09 Feb 2018","-0.451"
"2018-02-12","12 Feb 2018","-0.452"
"2018-02-13","13 Feb 2018","-0.451"
"2018-02-14","14 Feb 2018","-0.451"
"2018-02-15","15 Feb 2018","-0.441"
"2018-02-16","16 Feb 2018","-0.451"
"2018-02-19","19 Feb 2018","-0.453"
"2018-02-20","20 Feb 2018","-0.451"
"2018-02-21","21 Feb 2018","-0.450"
"2018-02-22","22 Feb 2018","-0.452"
"2018-02-23","23 Feb 2018","-0.453"
"2018-02-26","26 Feb 2018","-0.452"
"2018-02-27","27 Feb 2018","-0.453"
"2018-02-28","28 Feb 2018","-0.452"
"2018-03-01","01 Mar 2018","-0.451"
"2018-03-02","02 Mar 2018","-0.452"
"2018-03-05","05 Mar 2018","-0.454"
"2018-03-06","06 Mar 2018","-0.453"
"2018-03-07","07 Mar 2018","-0.452"
"2018-03-08","08 Mar 2018","-0.453"
"2018-03-09","09 Mar 2018","-0.452"
"2018-03-12","12 Mar 2018","-0.453"
"2018-03-13","13 Mar 2018","-0.454"
"2018-03-14","14 Mar 2018","-0.453"
"2018-03-15","15 Mar 2018","-0.454"
"2018-03-16","16 Mar 2018","-0.454"
"2018-03-19","19 Mar 2018","-0.455"
"2018-03-20","20 Mar 2018","-0.454"
"2018-03-21","21 Mar 2018","-0.454"
"2018-03-22","22 Mar 2018","-0.450"
"2018-03-23","23 Mar 2018","-0.452"
"2018-03-26","26 Mar 2018","-0.452"
"2018-03-27","27 Mar 2018","-0.451"
"2018-03-28","28 Mar 2018","-0.450"
"2018-03-29","29 Mar 2018","-0.458"
"2018-04-03","03 Apr 2018","-0.452"
"2018-04-04","04 Apr 2018","-0.454"
"2018-04-05","05 Apr 2018","-0.453"
"2018-04-06","06 Apr 2018","-0.453"
"2018-04-09","09 Apr 2018","-0.453"
"2018-04-10","10 Apr 2018","-0.453"
"2018-04-11","11 Apr 2018","-0.452"
"2018-04-12","12 Apr 2018","-0.452"
"2018-04-13","13 Apr 2018","-0.451"
"2018-04-16","16 Apr 2018","-0.453"
"2018-04-17","17 Apr 2018","-0.451"
"2018-04-18","18 Apr 2018","-0.450"
"2018-04-19","19 Apr 2018","-0.452"
"2018-04-20","20 Apr 2018","-0.452"
"2018-04-23","23 Apr 2018","-0.450"
"2018-04-24","24 Apr 2018","-0.452"
"2018-04-25","25 Apr 2018","-0.452"
"2018-04-26","26 Apr 2018","-0.450"
"2018-04-27","27 Apr 2018","-0.450"
"2018-04-30","30 Apr 2018","-0.450"
"2018-05-02","02 May 2018","-0.452"
"2018-05-03","03 May 2018","-0.453"
"2018-05-04","04 May 2018","-0.450"
"2018-05-07","07 May 2018","-0.453"
"2018-05-08","08 May 2018","-0.450"
"2018-05-09","09 May 2018","-0.448"
"2018-05-10","10 May 2018","-0.452"
"2018-05-11","11 May 2018","-0.451"
"2018-05-14","14 May 2018","-0.452"
"2018-05-15","15 May 2018","-0.452"
"2018-05-16","16 May 2018","-0.441"
"2018-05-17","17 May 2018","-0.451"
"2018-05-18","18 May 2018","-0.450"
"2018-05-21","21 May 2018","-0.452"
"2018-05-22","22 May 2018","-0.452"
"2018-05-23","23 May 2018","-0.454"
"2018-05-24","24 May 2018","-0.454"
"2018-05-25","25 May 2018","-0.450"
"2018-05-28","28 May 2018","-0.453"
"2018-05-29","29 May 2018","-0.460"
"2018-05-30","30 May 2018","-0.458"
"2018-05-31","31 May 2018","-0.457"
"2018-06-01","01 Jun 2018","-0.455"
"2018-06-04","04 Jun 2018","-0.455"
"2018-06-05","05 Jun 2018","-0.453"
"2018-06-06","06 Jun 2018","-0.452"
"2018-06-07","07 Jun 2018","-0.451"
"2018-06-08","08 Jun 2018","-0.452"
"2018-06-11","11 Jun 2018","-0.452"
"2018-06-12","12 Jun 2018","-0.452"
"2018-06-13","13 Jun 2018","-0.449"
"2018-06-14","14 Jun 2018","-0.450"
"2018-06-15","15 Jun 2018","-0.450"
"2018-06-18","18 Jun 2018","-0.451"
"2018-06-19","19 Jun 2018","-0.451"
"2018-06-20","20 Jun 2018","-0.450"
"2018-06-21","21 Jun 2018","-0.452"
"2018-06-22","22 Jun 2018","-0.451"
"2018-06-25","25 Jun 2018","-0.454"
"2018-06-26","26 Jun 2018","-0.452"
"2018-06-27","27 Jun 2018","-0.448"
"2018-06-28","28 Jun 2018","-0.451"
"2018-06-29","29 Jun 2018","-0.452"
"2018-07-02","02 Jul 2018","-0.450"
"2018-07-03","03 Jul 2018","-0.450"
"2018-07-04","04 Jul 2018","-0.451"
"2018-07-05","05 Jul 2018","-0.450"
"2018-07-06","06 Jul 2018","-0.451"
"2018-07-09","09 Jul 2018","-0.451"
"2018-07-10","10 Jul 2018","-0.450"
"2018-07-11","11 Jul 2018","-0.450"
"2018-07-12","12 Jul 2018","-0.450"
"2018-07-13","13 Jul 2018","-0.449"
"2018-07-16","16 Jul 2018","-0.450"
"2018-07-17","17 Jul 2018","-0.451"
"2018-07-18","18 Jul 2018","-0.451"
"2018-07-19","19 Jul 2018","-0.451"
"2018-07-20","20 Jul 2018","-0.451"
"2018-07-23","23 Jul 2018","-0.451"
"2018-07-24","24 Jul 2018","-0.449"
"2018-07-25","25 Jul 2018","-0.450"
"2018-07-26","26 Jul 2018","-0.450"
"2018-07-27","27 Jul 2018","-0.448"
"2018-07-30","30 Jul 2018","-0.448"
"2018-07-31","31 Jul 2018","-0.450"
"2018-08-01","01 Aug 2018","-0.449"
"2018-08-02","02 Aug 2018","-0.449"
"2018-08-03","03 Aug 2018","-0.449"
"2018-08-06","06 Aug 2018","-0.450"
"2018-08-07","07 Aug 2018","-0.451"
"2018-08-08","08 Aug 2018","-0.450"
"2018-08-09","09 Aug 2018","-0.449"
"2018-08-10","10 Aug 2018","-0.450"
"2018-08-13","13 Aug 2018","-0.450"
"2018-08-14","14 Aug 2018","-0.449"
"2018-08-15","15 Aug 2018","-0.449"
"2018-08-16","16 Aug 2018","-0.449"
"2018-08-17","17 Aug 2018","-0.450"
"2018-08-20","20 Aug 2018","-0.451"
"2018-08-21","21 Aug 2018","-0.449"
"2018-08-22","22 Aug 2018","-0.449"
"2018-08-23","23 Aug 2018","-0.449"
"2018-08-24","24 Aug 2018","-0.448"
"2018-08-27","27 Aug 2018","-0.449"
"2018-08-28","28 Aug 2018","-0.449"
"2018-08-29","29 Aug 2018","-0.448"
"2018-08-30","30 Aug 2018","-0.448"
"2018-08-31","31 Aug 2018","-0.449"
"2018-09-03","03 Sep 2018","-0.450"
"2018-09-04","04 Sep 2018","-0.451"
"2018-09-05","05 Sep 2018","-0.451"
"2018-09-06","06 Sep 2018","-0.453"
"2018-09-07","07 Sep 2018","-0.449"
"2018-09-10","10 Sep 2018","-0.449"
"2018-09-11","11 Sep 2018","-0.449"
"2018-09-12","12 Sep 2018","-0.435"
"2018-09-13","13 Sep 2018","-0.448"
"2018-09-14","14 Sep 2018","-0.449"
"2018-09-17","17 Sep 2018","-0.449"
"2018-09-18","18 Sep 2018","-0.449"
"2018-09-19","19 Sep 2018","-0.449"
"2018-09-20","20 Sep 2018","-0.449"
"2018-09-21","21 Sep 2018","-0.451"
"2018-09-24","24 Sep 2018","-0.451"
"2018-09-25","25 Sep 2018","-0.449"
"2018-09-26","26 Sep 2018","-0.451"
"2018-09-27","27 Sep 2018","-0.448"
"2018-09-28","28 Sep 2018","-0.454"
"2018-10-01","01 Oct 2018","-0.448"
"2018-10-02","02 Oct 2018","-0.449"
"2018-10-03","03 Oct 2018","-0.449"
"2018-10-04","04 Oct 2018","-0.449"
"2018-10-05","05 Oct 2018","-0.447"
"2018-10-08","08 Oct 2018","-0.448"
"2018-10-09","09 Oct 2018","-0.447"
"2018-10-10","10 Oct 2018","-0.448"
"2018-10-11","11 Oct 2018","-0.448"
"2018-10-12","12 Oct 2018","-0.447"
"2018-10-15","15 Oct 2018","-0.449"
"2018-10-16","16 Oct 2018","-0.449"
"2018-10-17","17 Oct 2018","-0.449"
"2018-10-18","18 Oct 2018","-0.449"
"2018-10-19","19 Oct 2018","-0.448"
"2018-10-22","22 Oct 2018","-0.449"
"2018-10-23","23 Oct 2018","-0.448"
"2018-10-24","24 Oct 2018","-0.448"
"2018-10-25","25 Oct 2018","-0.448"
"2018-10-26","26 Oct 2018","-0.448"
"2018-10-29","29 Oct 2018","-0.449"
"2018-10-30","30 Oct 2018","-0.449"
"2018-10-31","31 Oct 2018","-0.449"
"2018-11-01","01 Nov 2018","-0.449"
"2018-11-02","02 Nov 2018","-0.449"
"2018-11-05","05 Nov 2018","-0.449"
"2018-11-06","06 Nov 2018","-0.450"
"2018-11-07","07 Nov 2018","-0.448"
"2018-11-08","08 Nov 2018","-0.448"
"2018-11-09","09 Nov 2018","-0.449"
"2018-11-12","12 Nov 2018","-0.450"
"2018-11-13","13 Nov 2018","-0.449"
"2018-11-14","14 Nov 2018","-0.448"
"2018-11-15","15 Nov 2018","-0.435"
"2018-11-16","16 Nov 2018","-0.449"
"2018-11-19","19 Nov 2018","-0.449"
"2018-11-20","20 Nov 2018","-0.448"
"2018-11-21","21 Nov 2018","-0.448"
"2018-11-22","22 Nov 2018","-0.447"
"2018-11-23","23 Nov 2018","-0.448"
"2018-11-26","26 Nov 2018","-0.448"
"2018-11-27","27 Nov 2018","-0.448"
"2018-11-28","28 Nov 2018","-0.448"
"2018-11-29","29 Nov 2018","-0.448"
"2018-11-30","30 Nov 2018","-0.449"
"2018-12-03","03 Dec 2018","-0.449"
"2018-12-04","04 Dec 2018","-0.447"
"2018-12-05","05 Dec 2018","-0.449"
"2018-12-06","06 Dec 2018","-0.449"
"2018-12-07","07 Dec 2018","-0.449"
"2018-12-10","10 Dec 2018","-0.450"
"2018-12-11","11 Dec 2018","-0.450"
"2018-12-12","12 Dec 2018","-0.450"
"2018-12-13","13 Dec 2018","-0.449"
"2018-12-14","14 Dec 2018","-0.448"
"2018-12-17","17 Dec 2018","-0.449"
"2018-12-18","18 Dec 2018","-0.448"
"2018-12-19","19 Dec 2018","-0.449"
"2018-12-20","20 Dec 2018","-0.449"
"2018-12-21","21 Dec 2018","-0.449"
"2018-12-24","24 Dec 2018","-0.449"
"2018-12-27","27 Dec 2018","-0.448"
"2018-12-28","28 Dec 2018","-0.448"
"2018-12-31","31 Dec 2018","-0.464"
"2019-01-02","02 Jan 2019","-0.448"
"2019-01-03","03 Jan 2019","-0.449"
"2019-01-04","04 Jan 2019","-0.449"
"2019-01-07","07 Jan 2019","-0.450"
"2019-01-08","08 Jan 2019","-0.450"
"2019-01-09","09 Jan 2019","-0.449"
"2019-01-10","10 Jan 2019","-0.449"
"2019-01-11","11 Jan 2019","-0.449"
"2019-01-14","14 Jan 2019","-0.448"
"2019-01-15","15 Jan 2019","-0.449"
"2019-01-16","16 Jan 2019","-0.448"
"2019-01-17","17 Jan 2019","-0.448"
"2019-01-18","18 Jan 2019","-0.448"
"2019-01-21","21 Jan 2019","-0.449"
"2019-01-22","22 Jan 2019","-0.449"
"2019-01-23","23 Jan 2019","-0.448"
"2019-01-24","24 Jan 2019","-0.448"
"2019-01-25","25 Jan 2019","-0.448"
"2019-01-28","28 Jan 2019","-0.449"
"2019-01-29","29 Jan 2019","-0.449"
"2019-01-30","30 Jan 2019","-0.449"
"2019-01-31","31 Jan 2019","-0.449"
"2019-02-01","01 Feb 2019","-0.450"
"2019-02-04","04 Feb 2019","-0.451"
"2019-02-05","05 Feb 2019","-0.450"
"2019-02-06","06 Feb 2019","-0.451"
"2019-02-07","07 Feb 2019","-0.449"
"2019-02-08","08 Feb 2019","-0.449"
"2019-02-11","11 Feb 2019","-0.449"
"2019-02-12","12 Feb 2019","-0.449"
"2019-02-13","13 Feb 2019","-0.449"
"2019-02-14","14 Feb 2019","-0.450"
"2019-02-15","15 Feb 2019","-0.449"
"2019-02-18","18 Feb 2019","-0.451"
"2019-02-19","19 Feb 2019","-0.448"
"2019-02-20","20 Feb 2019","-0.432"
"2019-02-21","21 Feb 2019","-0.448"
"2019-02-22","22 Feb 2019","-0.448"
"2019-02-25","25 Feb 2019","-0.452"
"2019-02-26","26 Feb 2019","-0.449"
"2019-02-27","27 Feb 2019","-0.447"
"2019-02-28","28 Feb 2019","-0.449"
"2019-03-01","01 Mar 2019","-0.449"
"2019-03-04","04 Mar 2019","-0.450"
"2019-03-05","05 Mar 2019","-0.448"
"2019-03-06","06 Mar 2019","-0.449"
"2019-03-07","07 Mar 2019","-0.450"
"2019-03-08","08 Mar 2019","-0.448"
"2019-03-11","11 Mar 2019","-0.450"
"2019-03-12","12 Mar 2019","-0.451"
"2019-03-13","13 Mar 2019","-0.451"
"2019-03-14","14 Mar 2019","-0.451"
"2019-03-15","15 Mar 2019","-0.448"
"2019-03-18","18 Mar 2019","-0.451"
"2019-03-19","19 Mar 2019","-0.453"
"2019-03-20","20 Mar 2019","-0.452"
"2019-03-21","21 Mar 2019","-0.451"
"2019-03-22","22 Mar 2019","-0.451"
"2019-03-25","25 Mar 2019","-0.449"
"2019-03-26","26 Mar 2019","-0.449"
"2019-03-27","27 Mar 2019","-0.448"
"2019-03-28","28 Mar 2019","-0.451"
"2019-03-29","29 Mar 2019","-0.458"
"2019-04-01","01 Apr 2019","-0.449"
"2019-04-02","02 Apr 2019","-0.448"
"2019-04-03","03 Apr 2019","-0.452"
"2019-04-04","04 Apr 2019","-0.451"
"2019-04-05","05 Apr 2019","-0.451"
"2019-04-08","08 Apr 2019","-0.451"
"2019-04-09","09 Apr 2019","-0.452"
"2019-04-10","10 Apr 2019","-0.452"
"2019-04-11","11 Apr 2019","-0.453"
"2019-04-12","12 Apr 2019","-0.451"
"2019-04-15","15 Apr 2019","-0.450"
"2019-04-16","16 Apr 2019","-0.450"
"2019-04-17","17 Apr 2019","-0.448"
"2019-04-18","18 Apr 2019","-0.449"
"2019-04-23","23 Apr 2019","-0.449"
"2019-04-24","24 Apr 2019","-0.449"
"2019-04-25","25 Apr 2019","-0.450"
"2019-04-26","26 Apr 2019","-0.449"
"2019-04-29","29 Apr 2019","-0.448"
"2019-04-30","30 Apr 2019","-0.448"
"2019-05-02","02 May 2019","-0.451"
"2019-05-03","03 May 2019","-0.454"
"2019-05-06","06 May 2019","-0.455"
"2019-05-07","07 May 2019","-0.450"
"2019-05-08","08 May 2019","-0.450"
"2019-05-09","09 May 2019","-0.451"
"2019-05-10","10 May 2019","-0.450"
"2019-05-13","13 May 2019","-0.448"
"2019-05-14","14 May 2019","-0.450"
"2019-05-15","15 May 2019","-0.449"
"2019-05-16","16 May 2019","-0.450"
"2019-05-17","17 May 2019","-0.449"
"2019-05-20","20 May 2019","-0.449"
"2019-05-21","21 May 2019","-0.448"
"2019-05-22","22 May 2019","-0.430"
"2019-05-23","23 May 2019","-0.450"
"2019-05-24","24 May 2019","-0.451"
"2019-05-27","27 May 2019","-0.450"
"2019-05-28","28 May 2019","-0.448"
"2019-05-29","29 May 2019","-0.448"
"2019-05-30","30 May 2019","-0.450"
"2019-05-31","31 May 2019","-0.448"
"2019-06-03","03 Jun 2019","-0.451"
"2019-06-04","04 Jun 2019","-0.451"
"2019-06-05","05 Jun 2019","-0.450"
"2019-06-06","06 Jun 2019","-0.449"
"2019-06-07","07 Jun 2019","-0.450"
"2019-06-10","10 Jun 2019","-0.455"
"2019-06-11","11 Jun 2019","-0.449"
"2019-06-12","12 Jun 2019","-0.452"
"2019-06-13","13 Jun 2019","-0.452"
"2019-06-14","14 Jun 2019","-0.449"
"2019-06-17","17 Jun 2019","-0.450"
"2019-06-18","18 Jun 2019","-0.449"
"2019-06-19","19 Jun 2019","-0.450"
"2019-06-20","20 Jun 2019","-0.451"
"2019-06-21","21 Jun 2019","-0.450"
"2019-06-24","24 Jun 2019","-0.449"
"2019-06-25","25 Jun 2019","-0.450"
"2019-06-26","26 Jun 2019","-0.449"
"2019-06-27","27 Jun 2019","-0.451"
"2019-06-28","28 Jun 2019","-0.459"
"2019-07-01","01 Jul 2019","-0.450"
"2019-07-02","02 Jul 2019","-0.450"
"2019-07-03","03 Jul 2019","-0.452"
"2019-07-04","04 Jul 2019","-0.450"
"2019-07-05","05 Jul 2019","-0.450"
"2019-07-08","08 Jul 2019","-0.452"
"2019-07-09","09 Jul 2019","-0.452"
"2019-07-10","10 Jul 2019","-0.450"
"2019-07-11","11 Jul 2019","-0.450"
"2019-07-12","12 Jul 2019","-0.451"
"2019-07-15","15 Jul 2019","-0.450"
"2019-07-16","16 Jul 2019","-0.451"
"2019-07-17","17 Jul 2019","-0.452"
"2019-07-18","18 Jul 2019","-0.452"
"2019-07-19","19 Jul 2019","-0.452"
"2019-07-22","22 Jul 2019","-0.452"
"2019-07-23","23 Jul 2019","-0.451"
"2019-07-24","24 Jul 2019","-0.449"
"2019-07-25","25 Jul 2019","-0.450"
"2019-07-26","26 Jul 2019","-0.449"
"2019-07-29","29 Jul 2019","-0.449"
"2019-07-30","30 Jul 2019","-0.448"
"2019-07-31","31 Jul 2019","-0.449"
"2019-08-01","01 Aug 2019","-0.451"
"2019-08-02","02 Aug 2019","-0.452"
"2019-08-05","05 Aug 2019","-0.452"
"2019-08-06","06 Aug 2019","-0.452"
"2019-08-07","07 Aug 2019","-0.454"
"2019-08-08","08 Aug 2019","-0.452"
"2019-08-09","09 Aug 2019","-0.449"
"2019-08-12","12 Aug 2019","-0.451"
"2019-08-13","13 Aug 2019","-0.451"
"2019-08-14","14 Aug 2019","-0.451"
"2019-08-15","15 Aug 2019","-0.452"
"2019-08-16","16 Aug 2019","-0.454"
"2019-08-19","19 Aug 2019","-0.453"
"2019-08-20","20 Aug 2019","-0.450"
"2019-08-21","21 Aug 2019","-0.427"
"2019-08-22","22 Aug 2019","-0.449"
"2019-08-23","23 Aug 2019","-0.449"
"2019-08-26","26 Aug 2019","-0.450"
"2019-08-27","27 Aug 2019","-0.450"
"2019-08-28","28 Aug 2019","-0.451"
"2019-08-29","29 Aug 2019","-0.450"
"2019-08-30","30 Aug 2019","-0.448"
"2019-09-02","02 Sep 2019","-0.449"
"2019-09-03","03 Sep 2019","-0.449"
"2019-09-04","04 Sep 2019","-0.450"
"2019-09-05","05 Sep 2019","-0.449"
"2019-09-06","06 Sep 2019","-0.450"
"2019-09-09","09 Sep 2019","-0.450"
"2019-09-10","10 Sep 2019","-0.451"
"2019-09-11","11 Sep 2019","-0.450"
"2019-09-12","12 Sep 2019","-0.449"
"2019-09-13","13 Sep 2019","-0.451"
"2019-09-16","16 Sep 2019","-0.450"
"2019-09-17","17 Sep 2019","-0.451"
"2019-09-18","18 Sep 2019","-0.552"
"2019-09-19","19 Sep 2019","-0.555"
"2019-09-20","20 Sep 2019","-0.553"
"2019-09-23","23 Sep 2019","-0.554"
"2019-09-24","24 Sep 2019","-0.552"
"2019-09-25","25 Sep 2019","-0.553"
"2019-09-26","26 Sep 2019","-0.549"
"2019-09-27","27 Sep 2019","-0.547"
"2019-09-30","30 Sep 2019","-0.549"
