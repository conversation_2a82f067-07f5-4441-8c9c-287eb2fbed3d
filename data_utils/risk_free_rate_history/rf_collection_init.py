import pandas as pd
import numpy as np
from pymongo import MongoClient
from datetime import datetime

from configuration import config

settings = config.get_settings()

eonia_df = pd.read_csv('eonia.csv')
estr_df = pd.read_csv('estr.csv')

eonia_df['DATE'] = pd.to_datetime(eonia_df['DATE'])
estr_df['DATE'] = pd.to_datetime(estr_df['DATE'])

eonia_df = eonia_df.drop(columns=['TIME PERIOD'])
estr_df = estr_df.drop(columns=['TIME PERIOD'])

#In period of transition, EONIA was calculated as ESTR + 8.5 basis points.
eonia_df['EONIA'] = eonia_df['EONIA'] - 0.085

eonia_df.rename(columns={'EONIA': 'RATE'}, inplace=True)
estr_df.rename(columns={'ESTR': 'RATE'}, inplace=True)

risk_free_df = pd.concat([estr_df, eonia_df])

risk_free_df['RATE'] = np.round(risk_free_df['RATE'] / 100, 4)

risk_free_df = risk_free_df.sort_values(by=['DATE']).drop_duplicates(subset=['DATE'], keep='first')

# risk_free_df = risk_free_df.set_index('DATE').sort_index()

rf_documents = [
    {
        "date": row["DATE"].to_pydatetime(),
        "rate": row["RATE"]
    }
    for _, row in risk_free_df.iterrows()
]

with MongoClient(settings.db_url) as client:
    db = client[settings.db_name]
    risk_free_collection = db[settings.risk_free_rates_collection]

    risk_free_collection.insert_many(rf_documents)
