import pytz
import requests
from datetime import datetime
import sys
import os
import json
import numpy as np

from pymongo import MongoClient


#products_metadata = requests.get().json()
# tickers = ['XD9U.XETRA', 'IMAE.AS', 'LCUJ.XETRA', 'XMME.XETRA', 'IUSN.XETRA', 'IBTA.LSE',
#            'CSBGU0.SW', 'LQDA.LSE', 'IHYA.LSE', 'XGLE.LSE', '4GLD.XETRA', 'EPRA.PA']
# isins = ['IE00BJ0KDR00', 'IE00B4K48X80', 'LU1781541252', 'IE00BTJRMP35', 'IE00BF4RFH31', 'IE00BYXPSP02',
#          'IE00B3VWN518', 'IE00BYXYYJ35', 'IE00BYXYYL56', 'LU0290355717', 'DE000A0S9GB0', 'LU1437018838']
# usd_isins = ["IE00BYXYYJ35", "IE00BYXPSP02", "IE00BYXYYL56", "IE00B3VWN518"]

tickers = ['GAGG.LSE', 'VWCE.XETRA', '4GLD.XETRA', 'EPRA.PA']
asset_classes = ['Fixed Income', 'Equity', 'Commodity', 'Real Estate']
gbp = ['Fixed Income']

with MongoClient('mongodb+srv://valusense:<EMAIL>/') as client:
    db = client['dev']
    fx_rates = db['FX_rates']
    etf_collection = db['Asset_classes_prices_original']
    etf_collection_eur = db['Asset_classes_prices_EUR']

    prices = etf_collection.find()
    fx = fx_rates.find()

    date_and_fx = {}

    for rate in fx:
        date_and_fx[rate['date']] = rate['EUR']['GBP']

    for document in prices:
        eur_document = {'date': document['date'],}
        for key, value in document.items():
            if key in asset_classes:
                if key in gbp:
                    eur_document[key] = round(value / date_and_fx[document['date']], 4)
                else:
                    eur_document[key] = value
        etf_collection_eur.insert_one(eur_document)

