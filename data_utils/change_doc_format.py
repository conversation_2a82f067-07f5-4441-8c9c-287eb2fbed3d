from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime

# {
#   "date": "",
#   "isin_values": {},
#   "risk_free": {
#     "risk_free_isin": 0
#   }
# }

# Connect to MongoDB
client = MongoClient(
    'mongodb+srv://valusense:<EMAIL>/')  # MongoDB connection
db = client['dev']  # Replace with your database name
collection = db['ETF_prices_original']  # Replace with your collection name


# Function to format the document and identify fields to remove
def transform_document(doc):
    # Convert ObjectId to string (unchanged)
    _id = doc['_id']

    # Date remains unchanged
    date_str = doc['date']

    # Exclude '_id' and 'Date', move the rest to 'isinValues'
    isin_values = {key: value for key, value in doc.items() if key not in ['_id', 'date', 'FR0010510800']}

    # Identify fields to be removed from the root level
    fields_to_remove = {key: "" for key in isin_values}

    risk_free = {}
    risk_free['FR0010510800'] = doc['FR0010510800']

    fields_to_remove['FR0010510800'] = ""

    # Create the updated document structure
    updated_doc = {
        'date': date_str,
        'isin_values': isin_values,
        'risk_free': risk_free
    }

    return updated_doc, fields_to_remove

def transform_fx(doc):
    _id = doc['_id']

    # Date remains unchanged
    date_str = doc['date']

    eur_values = {key: value for key, value in doc.items() if key not in ['_id', 'date']}

    fields_to_remove = {key: "" for key in eur_values}

    updated_doc = {
        'date': date_str,
        'EUR': eur_values
    }

    return updated_doc, fields_to_remove

# Find and sort documents by 'Date' in ascending order
documents = collection.find().sort("date", 1)

# Iterate over sorted documents and update them
for doc in documents:
    updated_doc, fields_to_remove = transform_document(doc)

    # Update the document and remove the old ISIN fields
    collection.update_one({'_id': doc['_id']}, {'$set': updated_doc, '$unset': fields_to_remove})

print("Documents updated and old fields removed successfully!")