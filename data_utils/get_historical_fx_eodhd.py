import pytz
import requests
import pandas as pd
from pymongo import MongoClient
import json
from datetime import datetime

# List of tickers
tickers = ["EURUSD.FOREX", "EURGBP.FOREX", "EURCHF.FOREX"]


# API token (use your actual API token)
api_token = '67052a6bbe4108.46809416'

# Dictionary to hold data for DataFrame
data_dict = {}

# Iterate over the list of tickers
for ticker in tickers:
    # Construct the URL for the current ticker
    url = f'https://eodhd.com/api/eod/{ticker}?to=2024-10-15&period=d&api_token={api_token}&fmt=json&from=2019-02-22'

    # Fetch the data
    response = requests.get(url)

    # Check if the request was successful
    if response.status_code == 200:
        data = response.json()  # Parse the JSON response

        # Extract date and close price
        ticker_data = {}

        for product in data:
            if product['close'] != 'NA' and product['close'] != '':
                ticker_data[product['date']] = product['close']
            elif product['previousClose'] != 'NA' and product['previousClose'] != '':
                ticker_data[product['date']] = product['previousClose']
            else:
                ticker_data[product['date']] = 0

        # Add the extracted data to the dictionary under the ticker's name
        data_dict[ticker] = ticker_data
    else:
        print(f'Failed to retrieve data for {ticker}, Status code: {response.status_code}')


# Convert dictionary to DataFrame
etf_historical_prices_df = pd.DataFrame(data_dict)

#print(etf_historical_prices_df.iloc[130:160])



# Sort DataFrame by date (index)
etf_historical_prices_df.index = pd.to_datetime(etf_historical_prices_df.index)  # Convert index to datetime format
etf_historical_prices_df = etf_historical_prices_df.sort_index()

etf_historical_prices_df.columns = ["USD", "GBP", "CHF"]

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)

etf_historical_prices_df.fillna(method='ffill', inplace=True)


with MongoClient('mongodb+srv://valusense:<EMAIL>/') as client:
    db = client['dev']
    etf_prices_original_collection = db['FX_rates']

    for date, row in etf_historical_prices_df.iterrows():
        mongo_record = {
            'date': date.to_pydatetime().replace(tzinfo=pytz.UTC),  # Store date as a string in the desired format (e.g., YYYY-MM-DD)
        }

        # Add ISINs and their corresponding return values to the record
        for fx in ["USD", "GBP", "CHF"]:
            mongo_record[fx] = row[fx]

        #print(mongo_record)

        # Insert the record into MongoDB
        etf_prices_original_collection.insert_one(mongo_record)