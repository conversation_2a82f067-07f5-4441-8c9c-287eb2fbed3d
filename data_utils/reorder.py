from pymongo import MongoClient

# Connect to MongoDB
with MongoClient('mongodb+srv://valusense:<EMAIL>/') as client:
    db = client['dev']
    collection = db['ETF_prices_EUR_gld']

    # Iterate over all documents in the collection
    for doc in collection.find():
        isin_values = doc.get("isin_values", {})

        # If LU1437018838 exists in isin_values, reorder it
        if "LU1437018838" in isin_values:
            # Remove LU1437018838 from isin_values
            value_to_move = isin_values.pop("LU1437018838")

            # First, remove the old field
            collection.update_one(
                {"_id": doc["_id"]},
                {"$unset": {"isin_values.LU1437018838": ""}}
            )

            # Then, reinsert it at the end
            collection.update_one(
                {"_id": doc["_id"]},
                {"$set": {f"isin_values.LU1437018838": value_to_move}}
            )

