import numpy as np
from matplotlib import colormaps
import matplotlib.pyplot as plt
from typing import Tuple

from repositories.old_mongo_repositories import etf_repository, risk_free_rate_repository
from models import benchmark_model
from optimizers import efficient_frontier_optimizers, multi_period_optimizer
from configuration import config
from utils import simulation_utils
from utils.typing_util import float_array

settings = config.get_settings()

etf_repository = etf_repository.EtfRepository()
risk_free_rate_repository = risk_free_rate_repository.RiskFreeRateRepository()

pricing_model = benchmark_model.RegressionPricingModel()
capm_optimizer = efficient_frontier_optimizers.CapmOptimizer()
mp_optimizer = multi_period_optimizer.MultiPeriodOptimizer()

cmap_20 = colormaps.get_cmap('tab20')
colors_20 = [cmap_20(i / 19) for i in range(20)]

cmap_10 = colormaps.get_cmap('tab10')
colors_10 = [cmap_10(i / 9) for i in range(10)]

names = ['risk free', 'max sharpe', 'max return']
percentages = [0, 10, 30, 70, 90, 100]


def find_max_sharpe_and_max_risk_portfolios(efficient_frontier: np.ndarray,
                                            returns: np.ndarray,
                                            covariance: np.ndarray,
                                            current_risk_free_rate: float
                                            ) -> Tuple[np.ndarray, np.ndarray]:
    portfolio_returns = efficient_frontier @ returns

    portfolio_variances = np.array([weights @ covariance @ weights.T for weights in efficient_frontier]).reshape(-1, 1)
    portfolio_variances = np.where(portfolio_variances <= 1e-5, np.nan, portfolio_variances)

    portfolio_sharpe_ratios = (portfolio_returns - current_risk_free_rate) / np.sqrt(portfolio_variances)
    portfolio_sharpe_ratios = np.nan_to_num(portfolio_sharpe_ratios, nan=0.0)

    max_sharpe_index = np.argmax(portfolio_sharpe_ratios)
    max_return_index = np.argmax(portfolio_returns)

    return efficient_frontier[max_sharpe_index], efficient_frontier[max_return_index]


def calculate_returns_and_covariance_for_multi_period() -> Tuple[float, np.ndarray, np.ndarray]:
    avg_risk_free_rate = risk_free_rate_repository.get_avg_risk_free_rate_in_time_horizon(settings.time_horizon)
    current_risk_free_rate = risk_free_rate_repository.get_last_risk_free_rate()

    product_returns, product_covariance = pricing_model.calculate_products_returns_and_covariance(avg_risk_free_rate, current_risk_free_rate)

    efficient_frontier, _, _ = capm_optimizer.calculate_efficient_frontier(product_returns, product_covariance)

    max_sharpe_portfolio, max_return_portfolio = find_max_sharpe_and_max_risk_portfolios(efficient_frontier, product_returns, product_covariance, current_risk_free_rate)

    max_sharpe_return = max_sharpe_portfolio @ product_returns
    max_return = max_return_portfolio @ product_returns

    max_sharpe_variance = max_sharpe_portfolio @ product_covariance @ max_sharpe_portfolio.T
    max_variance = max_return_portfolio @ product_covariance @ max_return_portfolio.T
    sharpe_covariance = max_sharpe_portfolio @ product_covariance @ max_return_portfolio.T

    returns = np.hstack((max_sharpe_return, max_return))
    covariance = np.array([[max_sharpe_variance, sharpe_covariance],
                           [sharpe_covariance, max_variance]])

    return current_risk_free_rate, returns, covariance


def calculate_current_investments(investments: np.ndarray, current_return: float_array) -> np.ndarray:
    if investments.shape[0] >= 3:
        return np.vstack((investments[0] * (1 + current_return) + investments[1], investments[2:]))
    elif investments.shape[0] == 2:
        return investments[0] * (1 + current_return) + investments[1]
    else:
        return investments[0] * (1 + current_return)


def calculate_naive_return(risk_free_return: float,
                           max_return: float,
                           risk_free_weights: np.ndarray,
                           alphas: np.ndarray
                           ) -> float:
    n_periods = risk_free_weights.shape[0]

    cumulative_returns = np.vstack((
        [(1 + max_return) ** i - 1 for i in range(n_periods, 0, -1)],
        [(1 + risk_free_return) ** i - 1 for i in range(n_periods, 0, -1)]
    )).squeeze()

    weights = np.hstack((1 - risk_free_weights, risk_free_weights))

    return np.sum((alphas * weights) * cumulative_returns.T).astype(float)


def calculate_naive_max_sharpe_return(max_sharpe_return: float, alphas: np.ndarray) -> float:
    n_periods = alphas.shape[0]

    cumulative_ms_returns = np.array(
        [(1 + max_sharpe_return) ** i - 1 for i in range(n_periods, 0, -1)]
    ).reshape(-1, 1)

    return (alphas.T @ cumulative_ms_returns).item()


def calculate_cumulative_simulated_returns(simulated_returns: np.ndarray):
    # simulated product_returns have shape (n_sims, n_periods, n_assets=3)
    return (np.cumprod(1 + simulated_returns, axis=1) - 1)[:, -1, :]


def calculate_naive_terminal_wealths(simulated_returns: np.ndarray, investments: np.ndarray):
    weights_naive = np.zeros((simulated_returns.shape[1], simulated_returns.shape[2]))
    weights_naive[0, -1] = 1
    weights_naive[1:, 0] = 1

    n_periods = investments.shape[0]

    risk_free_return = simulated_returns[0, 0, 0]

    cumulative_rf = np.array([(1 + risk_free_return) ** i - 1 for i in range(n_periods - 1, 0, -1)]).reshape(-1, 1)
    cumulative_max_return = calculate_cumulative_simulated_returns(simulated_returns)[:, -1].reshape(-1, 1)

    first_return = ((1 + simulated_returns[:, 0, -1]) * (1 + cumulative_rf[0]) - 1).reshape(-1, 1)

    cumulative_rf = np.tile(cumulative_rf.T, (first_return.shape[0], 1))  # Shape becomes (100, n_periods-1)

    cumulative_r = np.hstack((cumulative_max_return, cumulative_rf))  # Shape becomes (100, n_periods)
    return (1 + cumulative_r) @ investments


def calculate_cumulative_simulated_max_sharpe_returns(simulated_returns: np.ndarray) -> np.ndarray:
    return np.flip(np.cumprod(1 + simulated_returns, axis=1) - 1, axis=1)[:, :, 1]

def calculate_max_sharpe_terminal_wealths(simulated_returns: np.ndarray, investments: np.ndarray) -> np.ndarray:
    cumulative_ms_returns = calculate_cumulative_simulated_max_sharpe_returns(simulated_returns)
    return (1 + cumulative_ms_returns) @ investments


def simulate_investment(risk_free_return: float,
                        returns: np.ndarray,
                        covariance: np.ndarray,
                        investments: np.ndarray,
                        invested_wealth: float,
                        target_return: float,
                        investment_horizon: int
                        ) -> Tuple[np.ndarray, np.ndarray, float, np.ndarray, np.ndarray, float, np.ndarray, np.ndarray]:
    sp_investments = np.copy(investments)

    comb_returns = np.hstack(([risk_free_return], returns))
    comb_cov = np.pad(covariance, pad_width=((1, 0), (1, 0)), mode='constant', constant_values=0)

    target_wealth = invested_wealth * (1 + target_return)

    mp_portfolios = []
    mp_period_wealths = []
    sp_portfolios = []
    sp_period_wealths = []
    simulated_returns = []
    feasibility = [False] * investment_horizon
    sp_target_return = target_return

    while investment_horizon > 0:
        mp_period_wealths.append(investments[0])
        sp_period_wealths.append(investments[0])

        annual_target_return = (1 + sp_target_return) ** (1 / investment_horizon) - 1

        (mp_risk_free_weights,
         mp_weights,
         multi_period_return,
         multi_period_variance,
         feasible) = mp_optimizer.calculate_multi_period_strategy(risk_free_return=risk_free_return,
                                                                  returns=returns,
                                                                  covariance=covariance,
                                                                  alphas=investments / investments.sum(),
                                                                  target_return=target_return)

        current_sp_portfolio, sp_expectation, sp_variance = capm_optimizer.calculate_strategy_for_target_return(comb_returns, comb_cov, annual_target_return)
        current_mp_portfolio = np.hstack((mp_risk_free_weights[0], mp_weights[0])).reshape(-1, 1)

        simulated_return = np.hstack(
            (risk_free_return,
             simulation_utils.sample_log_normal_from_arithmetic_parameters(
                 arithmetic_means=returns,
                 arithmetic_covariance=covariance,
                 n_periods=1,
                 n_samples=1
             ).squeeze())
        ).reshape(-1, 1)

        simulated_returns.append(simulated_return.T)

        current_mp_return = current_mp_portfolio.T @ simulated_return
        current_sp_return = current_sp_portfolio.T @ simulated_return

        investments = calculate_current_investments(investments, current_mp_return)
        sp_investments = calculate_current_investments(sp_investments, current_sp_return)
        target_return = target_wealth / investments.sum() - 1
        sp_target_return = target_wealth / sp_investments.sum() - 1

        mp_portfolios.append(current_mp_portfolio.T)
        sp_portfolios.append(current_sp_portfolio.T)
        feasibility[-investment_horizon] = feasible

        investment_horizon -= 1

    mp_period_wealths.append(investments[0])
    sp_period_wealths.append(sp_investments[0])

    mp_portfolios = np.vstack(mp_portfolios)
    mp_period_wealths = np.vstack(mp_period_wealths)

    sp_portfolios = np.vstack(sp_portfolios)
    sp_period_wealths = np.vstack(sp_period_wealths)

    simulated_returns = np.vstack(simulated_returns)
    feasibility = np.array(feasibility)

    return mp_portfolios, mp_period_wealths, investments.item(), sp_portfolios, sp_period_wealths, sp_investments.item(), simulated_returns, feasibility


def simulate_investment_multiple_times(risk_free_return: float,
                                       returns: np.ndarray,
                                       covariance: np.ndarray,
                                       investments: np.ndarray,
                                       target_return: float,
                                       investment_horizon: int,
                                       num_simulations: int = 100
                                       ) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    all_mp_portfolios = []
    all_mp_period_wealths = []
    mp_terminal_wealths = []
    all_sp_portfolios = []
    all_sp_period_wealths = []
    sp_terminal_wealths = []
    all_simulated_returns = []
    all_feasibility = []

    for _ in range(num_simulations):
        (mp_portfolios,
         mp_period_wealths,
         mp_terminal_wealth,
         sp_portfolios,
         sp_period_wealths,
         sp_terminal_wealth,
         simulated_returns,
         feasibility) = simulate_investment(
            risk_free_return=risk_free_return,
            returns=returns,
            covariance=covariance,
            investments=investments.copy(),
            invested_wealth=investments.sum(),
            target_return=target_return,
            investment_horizon=investment_horizon
        )

        all_mp_portfolios.append(mp_portfolios)
        all_mp_period_wealths.append(mp_period_wealths)
        mp_terminal_wealths.append(mp_terminal_wealth)
        all_sp_portfolios.append(sp_portfolios)
        all_sp_period_wealths.append(sp_period_wealths)
        sp_terminal_wealths.append(sp_terminal_wealth)
        all_simulated_returns.append(simulated_returns)
        all_feasibility.append(feasibility)

    all_mp_portfolios = np.stack(all_mp_portfolios, axis=0)
    all_mp_period_wealths = np.stack(all_mp_period_wealths, axis=0)
    mp_terminal_wealths = np.stack(mp_terminal_wealths, axis=0).reshape(-1, 1)
    all_sp_portfolios = np.stack(all_sp_portfolios, axis=0)
    all_sp_period_wealths = np.stack(all_sp_period_wealths, axis=0)
    sp_terminal_wealths = np.stack(sp_terminal_wealths, axis=0).reshape(-1, 1)
    all_simulated_returns = np.stack(all_simulated_returns, axis=0)
    all_feasibility = np.stack(all_feasibility, axis=0)

    return all_mp_portfolios, all_mp_period_wealths, mp_terminal_wealths, all_sp_portfolios, all_sp_period_wealths, sp_terminal_wealths, all_simulated_returns, all_feasibility


def simulate_expected_investment(risk_free_return: float,
                                 returns: np.ndarray,
                                 covariance: np.ndarray,
                                 investments: np.ndarray,
                                 invested_wealth: float,
                                 target_return: float,
                                 investment_horizon: int
                                 ) -> Tuple[np.ndarray, np.ndarray, float, np.ndarray]:
    target_wealth = invested_wealth * (1 + target_return)

    portfolios = []
    period_wealths = []
    feasibility = [False] * investment_horizon

    while investment_horizon > 0:
        period_wealths.append(investments[0])

        (risk_free_weights,
         weights,
         multi_period_return,
         multi_period_variance,
         feasible) = mp_optimizer.calculate_multi_period_strategy(risk_free_return=risk_free_return,
                                                                  returns=returns,
                                                                  covariance=covariance,
                                                                  alphas=investments / investments.sum(),
                                                                  target_return=target_return)

        current_portfolio = np.hstack((risk_free_weights[0], weights[0])).reshape(-1, 1)

        simulated_return = np.hstack((risk_free_return, returns))

        current_return = current_portfolio.T @ simulated_return

        investments = calculate_current_investments(investments, current_return)
        target_return = target_wealth / investments.sum() - 1

        portfolios.append(current_portfolio.T)
        feasibility[-investment_horizon] = feasible

        investment_horizon -= 1

    period_wealths.append(investments[0])

    portfolios = np.vstack(portfolios)
    period_wealths = np.vstack(period_wealths)
    feasibility = np.vstack(feasibility)

    return portfolios, period_wealths, investments.item(), feasibility


def plot_expected_multi_period_investment(risk_free_return: float,
                                          returns: np.ndarray,
                                          covariance: np.ndarray,
                                          investments: np.ndarray,
                                          invested_wealth: float,
                                          target_return: float,
                                          investment_horizon: int):
    (portfolios,
     period_wealths,
     terminal_wealth,
     feasibility) = simulate_expected_investment(risk_free_return=risk_free_return,
                                                 returns=returns,
                                                 covariance=covariance,
                                                 investments=investments,
                                                 invested_wealth=invested_wealth,
                                                 target_return=target_return,
                                                 investment_horizon=investment_horizon)

    variances = [weights[1:] @ covariance @ weights[1:].T for weights in portfolios]

    print(f'Target return: {target_return}')
    print(f'Terminal return: {terminal_wealth / (investment_horizon * 1000) - 1}')
    print(f'Target wealth: {invested_wealth * (1 + target_return)}')
    print(f'Terminal wealth: {terminal_wealth}')

    for i in range(portfolios.shape[1]):
        plt.plot(portfolios[:, i], label=names[i])
    plt.legend()
    plt.show()

    plt.plot(period_wealths)
    plt.plot(np.cumsum(investments), label='invested')
    plt.axhline(invested_wealth * (1 + target_return), label='target wealth', color='red')
    plt.legend()
    plt.show()

    plt.plot(variances)
    for i in range(returns.shape[0]):
        plt.axhline(covariance[i, i], color=colors_10[i], linestyle='--', label=names[i])
    plt.legend()
    plt.show()


def plot_expected_simulated_multi_period_investment(risk_free_return: float,
                                                    returns: np.ndarray,
                                                    covariance: np.ndarray,
                                                    investments: np.ndarray,
                                                    invested_wealth: float,
                                                    target_return: float,
                                                    investment_horizon: int,
                                                    num_simulations: int):
    (mp_portfolios,
     mp_period_wealths,
     mp_terminal_wealths,
     sp_portfolios,
     sp_period_wealths,
     sp_terminal_wealths,
     simulated_returns,
     feasibility) = simulate_investment_multiple_times(
        risk_free_return=risk_free_return,
        returns=returns,
        covariance=covariance,
        investments=investments,
        target_return=target_return,
        investment_horizon=investment_horizon,
        num_simulations=num_simulations
    )

    mean_mp_portfolios = np.mean(mp_portfolios, axis=0)
    mean_mp_variances = [weights[1:] @ covariance @ weights[1:].T for weights in mean_mp_portfolios]
    mean_mp_period_wealths = np.mean(mp_period_wealths, axis=0)
    mean_mp_terminal_wealth = np.mean(mp_terminal_wealths)
    mean_sp_portfolios = np.mean(sp_portfolios, axis=0)
    mean_sp_variances = [weights[1:] @ covariance @ weights[1:].T for weights in mean_sp_portfolios]
    mean_sp_period_wealths = np.mean(sp_period_wealths, axis=0)
    mean_sp_terminal_wealth = np.mean(sp_terminal_wealths)
    mean_simulated_returns = np.mean(simulated_returns, axis=0)

    print(mean_sp_variances)
    print(mean_mp_variances)

    print(f'Target return: {target_return}')
    print(f'Terminal return: {mean_mp_terminal_wealth / (investment_horizon * 1000) - 1}')
    print(f'Target Wealth: {invested_wealth * (1 + target_return)}')
    print(f'Mean Terminal Wealth: {mean_mp_terminal_wealth}')

    for i in range(mean_mp_portfolios.shape[1]):
        plt.plot(mean_mp_portfolios[:, i], label=names[i])
    plt.title('Mean Multi-Period Portfolios')
    plt.legend()
    plt.show()

    for i in range(mean_sp_portfolios.shape[1]):
        plt.plot(mean_sp_portfolios[:, i], label=names[i])
    plt.title('Mean Single-Period Portfolios')
    plt.legend()
    plt.show()

    for i in range(1, mean_simulated_returns.shape[1]):
        plt.plot(mean_simulated_returns[:, i], label=names[i], color=colors_10[i - 1])

    for i in range(returns.shape[0]):
        plt.axhline(returns[i], color=colors_10[i], linestyle='--')

    plt.legend()
    plt.show()

    plt.plot(mean_mp_period_wealths, label='multi-period')
    plt.plot(mean_sp_period_wealths, label='single-period')
    plt.plot(np.cumsum(investments), label='invested')
    plt.axhline(invested_wealth * (1 + target_return), label='target wealth', color='red')
    plt.title('Wealth over periods')
    plt.legend()
    plt.show()

    plt.plot(mean_mp_variances, label='multi-period')
    plt.plot(mean_sp_variances, label='single-period')
    plt.title('Variances over periods')
    plt.legend()
    plt.show()

    naive_terminal_wealths = calculate_naive_terminal_wealths(simulated_returns, investments)

    plt.hist(naive_terminal_wealths, bins=num_simulations // 50, density=True, color=colors_10[3], label='naive')
    plt.hist(mp_terminal_wealths, bins=num_simulations // 50, density=True, color=colors_10[1], label='multi-period', alpha=1)
    plt.hist(sp_terminal_wealths, bins=num_simulations // 50, density=True, color=colors_10[2], label='single-period', alpha=0.7)
    plt.axvline(invested_wealth, color=colors_10[3], linestyle='--', label='invested')
    plt.axvline(invested_wealth * (1 + target_return), color=colors_10[-2], linestyle='--', label='target')
    plt.legend()
    plt.show()

    return mp_portfolios, mp_period_wealths, mp_terminal_wealths, sp_portfolios, sp_period_wealths, sp_terminal_wealths, naive_terminal_wealths, simulated_returns


def plot_scenarios_based_on_simulated_wealths(returns: np.ndarray,
                                              covariance: np.ndarray,
                                              investments: np.ndarray,
                                              portfolios: np.ndarray,
                                              period_wealths: np.ndarray,
                                              terminal_wealths: np.ndarray,
                                              naive_terminal_wealths: np.ndarray,
                                              simulated_returns: np.ndarray,
                                              target_return: float,
                                              num_simulations: int,
                                              with_respect_to_naive: bool = False):
    default_terminal_wealths = naive_terminal_wealths if with_respect_to_naive else terminal_wealths
    default_name = 'Naive Terminal Wealths' if with_respect_to_naive else 'Optimized Terminal Wealths'

    for lower_percentage, higher_percentage in zip(percentages[:-1], percentages[1:]):
        lower_percentile = np.percentile(default_terminal_wealths, lower_percentage)
        higher_percentile = np.percentile(default_terminal_wealths, higher_percentage)

        indices_between_percentiles = np.where((default_terminal_wealths > lower_percentile) & (default_terminal_wealths < higher_percentile))[0]

        portfolios_between_percentiles = portfolios[indices_between_percentiles]
        terminal_wealths_between_percentiles = terminal_wealths[indices_between_percentiles]
        period_wealths_between_percentiles = period_wealths[indices_between_percentiles]
        simulated_returns_between_percentiles = simulated_returns[indices_between_percentiles]

        mean_portfolio_between_percentiles = np.mean(portfolios_between_percentiles, axis=0)
        mean_period_wealths_between_percentiles = np.mean(period_wealths_between_percentiles, axis=0)
        mean_simulated_returns_between_percentiles = np.mean(simulated_returns_between_percentiles, axis=0)
        mean_variances_between_percentiles = [weights[1:] @ covariance @ weights[1:].T for weights in mean_portfolio_between_percentiles]

        fig,axes = plt.subplots(nrows=3, ncols=2, figsize=(20, 30), constrained_layout=True)
        fig.suptitle(f'Data between {lower_percentage} and {higher_percentage} percentiles of {default_name}', fontsize=20)

        for i in range(mean_portfolio_between_percentiles.shape[1]):
            axes[0, 0].plot(mean_portfolio_between_percentiles[:, i], label=names[i])
        axes[0, 0].legend()
        axes[0, 0].set_title("FrontierPortfolio Weights")

        for i in range(1, mean_simulated_returns_between_percentiles.shape[1]):
            axes[1, 0].plot(mean_simulated_returns_between_percentiles[:, i], label=names[i], color=colors_10[i - 1])

        for i in range(returns.shape[0]):
            axes[1, 0].axhline(returns[i], color=colors_10[i], linestyle='--')

        axes[1, 0].legend()
        axes[1, 0].set_title("Simulated Returns")

        axes[2, 0].plot(mean_variances_between_percentiles)
        for i in range(returns.shape[0]):
            axes[2, 0].axhline(covariance[i, i], color=colors_10[i], linestyle='--', label=names[i])
        axes[2, 0].legend()
        axes[2, 0].set_title("Variances")

        axes[0, 1].plot(mean_period_wealths_between_percentiles, label='wealth')
        axes[0, 1].plot(np.cumsum(investments), label='invested')
        axes[0, 1].axhline(sum(investments) * (1 + target_return), label='target wealth', color='red')
        axes[0, 1].legend()
        axes[0, 1].set_title("Period Wealths")

        naive_terminal_wealths_between_percentiles = calculate_naive_terminal_wealths(simulated_returns_between_percentiles, investments)

        min_terminal_wealth = np.min(np.concatenate([naive_terminal_wealths_between_percentiles, terminal_wealths_between_percentiles]))
        max_terminal_wealth = np.max(np.concatenate([naive_terminal_wealths_between_percentiles, terminal_wealths_between_percentiles]))

        bins = np.linspace(min_terminal_wealth, max_terminal_wealth, num_simulations // 50)

        axes[1, 1].hist(naive_terminal_wealths_between_percentiles, bins=bins, density=True, color=colors_10[2], label='naive')
        axes[1, 1].hist(terminal_wealths_between_percentiles, bins=bins, density=True, color=colors_10[1], label='optimized', alpha=0.7)
        axes[1, 1].axvline(np.sum(investments), color=colors_10[3], linestyle='--', label='invested')
        axes[1, 1].axvline(np.sum(investments) * (1 + target_return), color=colors_10[-2], linestyle='--', label='target')
        axes[1, 1].legend()
        axes[1, 1].set_title("Histogram of Terminal Wealths")

        plt.show()
        print()
        print()
        print()
