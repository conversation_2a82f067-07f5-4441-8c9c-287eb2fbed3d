from typing import List

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.ticker as mtick
import seaborn as sns

from schemas.responses import BacktestResponse
from utils import util
from repositories.old_mongo_repositories import etf_repository, metadata_repository

from configuration import config

settings = config.get_settings()
etf_repository = etf_repository.EtfRepository()
metadata_repository = metadata_repository.MetadataRepository()

bar_palette = sns.color_palette('Set2', n_colors=5)
heatmap_palette = sns.diverging_palette(220, 10, as_cmap=True)
stacked_area_palette = sns.color_palette('tab20')


def load_lambda(prefix: str) -> float:
    lambda_ = pd.read_csv(f'{settings.analytics_path}{prefix}/lambda.csv')
    return lambda_.values.item()


def load_and_plot_betas(prefix: str) -> plt.Axes:
    betas = pd.read_csv(f'{settings.analytics_path}{prefix}/betas.csv')

    products_dict = metadata_repository.get_fields_for_ISINs(betas.columns.tolist(), ['assetClass', 'fundName'])

    products_asset_class, products_name = products_dict['assetClass'], products_dict['fundName']

    betas = pd.melt(betas, var_name='Product', value_name='Beta')
    betas['Asset Class'] = products_asset_class

    plt.figure(figsize=(10, 6))
    ax = sns.barplot(x='Product', y='Beta', hue='Asset Class', data=betas, palette=bar_palette)

    ax.set_title('Betas of the Products to the Market Portfolio')
    ax.set_xlabel('Products')
    ax.set_ylabel('Betas')

    for container in ax.containers:
        ax.bar_label(container, fmt='%.4f')

    ax.set_xticklabels(products_name, rotation=90)

    return ax


def load_and_plot_expected_returns(prefix: str, file_name: str) -> plt.Axes:
    expected_products_returns = pd.read_csv(f'{settings.analytics_path}{prefix}/{file_name}')
    isins = expected_products_returns.columns.values.tolist()

    products_dict = metadata_repository.get_fields_for_ISINs(isins, ['assetClass', 'fundName'])

    products_asset_classes, products_names = products_dict['assetClass'], products_dict['fundName']

    expected_products_returns = pd.melt(expected_products_returns, var_name='Product', value_name='Return')
    expected_products_returns['Asset Class'] = products_asset_classes

    plt.figure(figsize=(10, 8))
    ax = sns.barplot(x='Product', y='Return', hue='Asset Class', data=expected_products_returns, palette=bar_palette)

    if file_name == 'etf_expected_returns.csv':
        ax.set_title(f'Expected Returns of the Products')
    elif file_name == 'etf_expected_returns_with_ter.csv':
        ax.set_title(f'Expected Returns of the Products with TER')
    ax.set_xlabel('Products')
    ax.set_ylabel('Expected returns')

    for container in ax.containers:
        ax.bar_label(container, fmt='%.4f')

    ax.set_xticklabels(products_names, rotation=90)

    return ax


def load_and_plot_products_covariance(prefix: str) -> (plt.Axes, np.ndarray):
    products_covariance = pd.read_csv(f'{settings.analytics_path}{prefix}/etf_covariance.csv')
    isins = products_covariance.columns.values.tolist()

    products_variances, products_correlation = util.variances_and_correlation_from_covariance(products_covariance)

    fig, axes = plt.subplots(nrows=1, ncols=2, figsize=(20, 8))
    sns.heatmap(products_correlation, annot=True, fmt='.2f', linewidths=.5, ax=axes[0], cmap=heatmap_palette)

    axes[0].set_title('Correlation Matrix of the Products')
    axes[0].set_xlabel('Products')
    axes[0].set_ylabel('Products')

    products_dict = metadata_repository.get_fields_for_ISINs(isins, ['assetClass', 'fundName'])

    products_asset_classes, products_names = products_dict['assetClass'], products_dict['fundName']

    variances = pd.melt(products_variances, var_name='Product', value_name='Variance')
    variances['Asset Class'] = products_asset_classes

    sns.barplot(x='Product', y='Variance', hue='Asset Class', data=variances, palette=bar_palette, ax=axes[1])

    axes[1].set_title('Variances of the Products')
    axes[1].set_xlabel('Products')
    axes[1].set_ylabel('Variances')
    axes[1].set_xticklabels(products_names, rotation=90)

    for container in axes[1].containers:
        axes[1].bar_label(container, fmt='%.4f')

    return axes, products_variances


def plot_efficient_frontier(prefix: str) -> plt.Axes:
    expected_products_returns = pd.read_csv(f'{settings.analytics_path}{prefix}/etf_expected_returns.csv')
    etf_covariance = pd.read_csv(f'{settings.analytics_path}{prefix}/etf_covariance.csv')
    etf_stds = np.sqrt(np.diag(etf_covariance.values))

    isins = expected_products_returns.columns.values.tolist()

    products_names = metadata_repository.get_fields_for_ISINs(isins, ['fundName'])['fundName']

    efficient_frontier = pd.read_csv(f'{settings.analytics_path}{prefix}/efficient_frontier.csv')
    efficient_frontier['standard_deviation'] = np.sqrt(efficient_frontier['variance'])

    plt.figure(figsize=(10, 8))
    sns.scatterplot(efficient_frontier, x='standard_deviation', y='return', s=75, label='Efficient Frontier')

    ax = sns.scatterplot(x=etf_stds, y=expected_products_returns.values.reshape(-1), s=100,
                         hue=products_names,
                         marker='X', palette=stacked_area_palette)

    plt.title('Standard Deviation - Return plot of Efficient Frontier and Products')
    plt.xlabel('Standard Deviation')
    plt.ylabel('Returns')
    plt.legend(fontsize=6)

    return ax


def plot_discrete_efficient_frontier(prefix: str, investment_amount: str) -> plt.Axes:
    expected_products_returns = pd.read_csv(f'{settings.analytics_path}{prefix}/etf_expected_returns.csv')
    etf_covariance = pd.read_csv(f'{settings.analytics_path}{prefix}/etf_covariance.csv')
    etf_stds = np.sqrt(np.diag(etf_covariance.values))

    discrete_efficient_frontier = pd.read_csv(f'{settings.analytics_path}{prefix}/{investment_amount}/discrete_efficient_frontier.csv')
    discrete_efficient_frontier['standard_deviation'] = np.sqrt(discrete_efficient_frontier['variance'])

    isins = expected_products_returns.columns.values.tolist()

    products_names = metadata_repository.get_fields_for_ISINs(isins, ['fundName'])['fundName']

    plt.figure(figsize=(10, 8))
    sns.scatterplot(discrete_efficient_frontier, x='standard_deviation', y='return', s=75, label='Efficient Frontier')
    ax = sns.scatterplot(x=etf_stds, y=expected_products_returns.values.reshape(-1), s=100,
                         hue=products_names,
                         marker='X', palette=stacked_area_palette)

    plt.title(f'Standard Deviation - Return plot of Discrete Efficient Frontier and Products for {investment_amount}€')
    plt.xlabel('Standard Deviation')
    plt.ylabel('Returns')
    plt.legend(fontsize=6)

    return ax


def plot_inefficiency_of_discrete_efficient_frontier(prefix: str, investment_amount: str) -> plt.Axes:
    prices_df, risk_free_price_df = etf_repository.get_prices_eur()
    prices_df = pd.concat([prices_df, risk_free_price_df], axis=1)

    prices = prices_df.values.reshape(-1, 1)

    quantities_discrete_frontier = pd.read_csv(f'{settings.analytics_path}{prefix}/{investment_amount}/quantities_discrete_efficient_frontier.csv')
    investment_amount = float(investment_amount)

    quantities_discrete_frontier['inefficiency_percentage'] = ((investment_amount - np.dot(quantities_discrete_frontier, prices)) / investment_amount * 100)

    ax = sns.lineplot(quantities_discrete_frontier['inefficiency_percentage'])
    ax.yaxis.set_major_formatter(mtick.PercentFormatter())
    plt.title(f'Inefficiency of Discrete Efficient Frontier for {investment_amount}€')
    plt.ylabel('Inefficiency Percentage')

    return ax


def plot_weights_in_efficient_frontier(prefix: str) -> plt.Axes:
    efficient_frontier = pd.read_csv(f'{settings.analytics_path}{prefix}/efficient_frontier.csv')
    efficient_frontier.drop(columns=['return', 'variance', 'sharpe'], inplace=True)

    isins = efficient_frontier.columns.values.tolist()

    products_names = metadata_repository.get_fields_for_ISINs(isins, ['fundName'])['fundName']

    plt.figure(figsize=(10, 8))
    ax = plt.stackplot(range(100), efficient_frontier.T, labels=products_names, colors=stacked_area_palette)

    plt.title('Weights in Efficient Frontier')
    plt.legend(loc=(1.04, 0))

    return ax


def plot_weights_in_discrete_efficient_frontier(prefix: str, investment_amount: str) -> plt.Axes:
    discrete_efficient_frontier = pd.read_csv(f'{settings.analytics_path}{prefix}/{investment_amount}/discrete_efficient_frontier.csv')
    discrete_efficient_frontier.drop(columns=['return', 'variance', 'sharpe'], inplace=True)

    isins = discrete_efficient_frontier.columns.values.tolist()

    products_names = metadata_repository.get_fields_for_ISINs(isins, ['fundName'])['fundName']

    plt.figure(figsize=(10, 8))
    ax = plt.stackplot(range(100), discrete_efficient_frontier.T, labels=products_names, colors=stacked_area_palette)

    plt.title(f'Weights in Discrete Efficient Frontier for {investment_amount}€')
    plt.legend(loc=(1.04, 0))

    return ax

def plot_portfolio_values_backtest(response: BacktestResponse, response_without_rebalance: BacktestResponse, msci_usa_response: BacktestResponse, ax: plt.Axes) -> plt.Axes:
    dates = [point.point_datetime for point in response.points]
    values = [point.portfolio_value for point in response.points]
    values_without_rebalance = [point.portfolio_value for point in response_without_rebalance.points]

    msci_values = [point.portfolio_value for point in msci_usa_response.points]

    ax.plot(dates, values, label='Tested portfolio')
    ax.plot(dates, values_without_rebalance, label='Tested portfolio without rebalance')
    ax.plot(dates, msci_values, label='MSCI USA')
    ax.set_title('Portfolio Values')
    ax.legend()
    return ax

def plot_weights_in_backtest(response: BacktestResponse, isins: List[str], ax: plt.Axes) -> plt.Axes:
    dates = [point.point_datetime for point in response.points]
    weights = np.array([point.weights for point in response.points])

    ax.stackplot(dates, weights.T, labels=isins,  colors=stacked_area_palette)

    ax.set_title(f'Weights in tested Portfolio')
    ax.legend()

    return ax