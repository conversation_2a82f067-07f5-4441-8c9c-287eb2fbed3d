from abc import ABC, abstractmethod
from sys import prefix

import numpy as np

from configuration.config import get_settings
from schemas.constants import RiskMeasureEnum


class RiskMeasure(ABC):
    def __init__(self):
        self.settings = get_settings()

    @abstractmethod
    def calculate_risk(self, simulations: np.ndarray, **kwargs) -> np.ndarray:
        pass


class VolatilityMeasure(RiskMeasure):
    def calculate_risk(self, simulations: np.ndarray, **kwargs) -> np.ndarray:
        return np.std(simulations, axis=0)


class VaRMeasure(RiskMeasure):
    def calculate_risk(self, simulations: np.ndarray, **kwargs) -> np.ndarray:
        percentile = kwargs.get('percentile', self.settings.default_risk_percentile)
        return -np.percentile(simulations, percentile, axis=0)


class CVaRMeasure(RiskMeasure):
    def calculate_risk(self, simulations: np.ndarray, **kwargs) -> np.ndarray:
        percentile = kwargs.get('percentile', self.settings.default_risk_percentile)
        n_simulations = simulations.shape[0]
        tail_size = int(np.ceil(percentile/100 * n_simulations))
        # print(percentile)
        # print(tail_size)
        use_best_tail = tail_size > n_simulations / 2

        # Sort each column descending (best to worst)
        sorted_desc = np.sort(simulations, axis=0)[::-1, :]

        if use_best_tail:
            # Calculate mean of best returns (reward metric)
            best_returns = sorted_desc[:-tail_size, :]
            return -np.mean(best_returns, axis=0)
        else:
            # Calculate negative mean of worst returns (risk metric)
            worst_returns = sorted_desc[-tail_size:, :]
            return -np.mean(worst_returns, axis=0)




class RiskMeasureFactory:
    @staticmethod
    def create_risk_measure(risk_measure_type: RiskMeasureEnum) -> RiskMeasure:
        if risk_measure_type == RiskMeasureEnum.VOLATILITY:
            return VolatilityMeasure()
        elif risk_measure_type == RiskMeasureEnum.VAR:
            return VaRMeasure()
        elif risk_measure_type == RiskMeasureEnum.CVAR:
            return CVaRMeasure()
        else:
            raise ValueError(f"Invalid risk measure type: {risk_measure_type}")
