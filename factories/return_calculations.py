from abc import ABC, abstractmethod
import numpy as np

from schemas.constants import ReturnCalculationEnum


class ReturnCalculator(ABC):

    @abstractmethod
    def calculate_strategy_returns(self, simulated_returns: np.ndarray, historical_returns: np.ndarray = None, **kwargs) -> np.ndarray:
        pass

    @abstractmethod
    def calculate_cumulative_returns(self, simulated_returns: np.ndarray, **kwargs) -> np.ndarray:
        pass

    @abstractmethod
    def calculate_cumulative_return(self, simulated_returns: np.ndarray, **kwargs) -> float:
        pass

    @staticmethod
    def _slice_returns(simulated_returns: np.ndarray) -> np.ndarray:
        n_sims = simulated_returns.shape[0]
        n_periods = simulated_returns.shape[1]

        starting_indices = np.arange(n_periods)
        ending_indices = np.arange(n_sims - n_periods + 1, n_sims + 1)

        sliced_returns = np.vstack(
            [simulated_returns[start:end, i] for i, (start, end) in enumerate(zip(starting_indices, ending_indices))]
        ).T

        return sliced_returns

    @staticmethod
    def _calculate_cumulative_returns(simulated_returns: np.ndarray) -> np.ndarray:
        if simulated_returns.ndim == 1:
            simulated_returns = simulated_returns.reshape(1, -1)

        log_returns = np.log1p(simulated_returns[:, ::-1])
        cumulative_log_returns = np.cumsum(log_returns, axis=1)[:, ::-1]
        cumulative_returns = np.expm1(cumulative_log_returns)

        return cumulative_returns


class TimeWeightedReturnCalculator(ReturnCalculator):
    def calculate_strategy_returns(self, simulated_returns: np.ndarray, **kwargs) -> np.ndarray:
        cumulative_returns = self.calculate_cumulative_return(simulated_returns, **kwargs)

        return cumulative_returns[:, 0].reshape(-1, 1)

    def calculate_cumulative_returns(self, simulated_returns: np.ndarray, **kwargs) -> np.ndarray:
        return self._calculate_cumulative_returns(simulated_returns)

    def calculate_cumulative_return(self, simulated_returns: np.ndarray, **kwargs) -> float:
        return self.calculate_cumulative_returns(simulated_returns, **kwargs)[0].item()


class MoneyWeightedReturnCalculator(ReturnCalculator):
    @staticmethod
    def _get_alphas(**kwargs) -> np.ndarray:
        alphas = kwargs.get('alphas')
        if alphas is None:
            raise TypeError('Missing required argument alphas')
        return alphas.reshape(-1, 1)

    def calculate_strategy_returns(self, simulated_returns: np.ndarray, **kwargs) -> np.ndarray:
        cumulative_returns = self.calculate_cumulative_returns(simulated_returns, **kwargs)

        return cumulative_returns

    def calculate_cumulative_returns(self, simulated_returns: np.ndarray, **kwargs) -> np.ndarray:
        alphas = self._get_alphas(**kwargs)

        cumulative_returns = self._calculate_cumulative_returns(simulated_returns)
        return cumulative_returns @ alphas

    def calculate_cumulative_return(self, simulated_returns: np.ndarray, **kwargs) -> float:
        simulated_returns = simulated_returns.reshape(1, -1)
        return self.calculate_cumulative_returns(simulated_returns, **kwargs).item()


class ReturnCalculatorFactory:
    @staticmethod
    def create_return_calculator(return_calculator_type: ReturnCalculationEnum) -> ReturnCalculator:
        if return_calculator_type == ReturnCalculationEnum.TWR:
            return TimeWeightedReturnCalculator()
        elif return_calculator_type == ReturnCalculationEnum.MWR:
            return MoneyWeightedReturnCalculator()
        else:
            raise ValueError(f'Unsupported return calculator type: {return_calculator_type}')
