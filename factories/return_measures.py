from abc import ABC, abstractmethod
import numpy as np

from configuration.config import get_settings
from schemas.constants import ReturnMeasureEnum


class ReturnMeasure(ABC):
    def __init__(self):
        self.settings = get_settings()

    @abstractmethod
    def calculate_return(self, simulations: np.ndarray) -> np.ndarray:
        pass


class ExpectedReturnMeasure(ReturnMeasure):
    def calculate_return(self, simulations: np.ndarray) -> np.ndarray:
        return np.mean(simulations, axis=0)


class MedianReturnMeasure(ReturnMeasure):
    def calculate_return(self, simulations: np.ndarray) -> np.ndarray:
        return np.median(simulations, axis=0)


class ReturnMeasureFactory:
    @staticmethod
    def create_return_measure(return_measure_type: ReturnMeasureEnum) -> ReturnMeasure:
        if return_measure_type == ReturnMeasureEnum.EXPECTED:
            return ExpectedReturnMeasure()
        elif return_measure_type == ReturnMeasureEnum.MEDIAN:
            return MedianReturnMeasure()
        else:
            raise ValueError(f"Unsupported return measure type: {return_measure_type}")
