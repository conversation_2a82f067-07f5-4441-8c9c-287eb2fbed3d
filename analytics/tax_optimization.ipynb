#%%

%load_ext autoreload
%autoreload 2
#%%


import os
from pathlib import Path
from datetime import datetime
import numpy as np

from configuration import config
from requests_.requests import EfficientFrontierRequest, BacktestRequest
from responses_.responses import EfficientFrontierResponse
from main import calculate_efficient_frontier
from repositories.old_mongo_repositories import etf_repository
#%%
import pandas as pd
import matplotlib.pyplot as plt

# Read the CSV file, specifying the unnamed date column using its index (0 for the first column)
df = pd.read_csv("etf_prices2.csv", header=0)

# Rename the unnamed date column for easier handling
df.rename(columns={df.columns[0]: "Date"}, inplace=True)

# Convert the 'Date' column to datetime format
df['Date'] = pd.to_datetime(df['Date'])

# Set 'Date' as the index for plotting
df.set_index('Date', inplace=True)

# Plot each ISIN price
plt.figure(figsize=(12, 8))
for isin in df.columns:
    plt.plot(df.index, df[isin], label=isin)  # Plot each ISIN

# Customize the plot
plt.xlabel("Date")
plt.ylabel("Price")
plt.title("ISIN Prices Over Time")
plt.legend()  # Add a legend to differentiate each ISIN
plt.grid(visible=True, linestyle='--', alpha=0.5)
plt.tight_layout()  # Adjust layout for better fit

# Show the plot
plt.show()
#%%
isins = ['IE00BJ0KDR00', 'IE00B4K48X80', 'LU1781541252', 'IE00BTJRMP35', 'IE00BF4RFH31',
         'IE00BGSF1X88', 'IE00BYXPSP02', 'IE00B3VWN518', 'IE00B1FZSC47', 'IE00BYXYYJ35', 'IE00BF11F565', 'IE00BYXYYL56', 'LU0290355717',
         'DE000A0S9GB0',
         'LU1437018838']
#%%
import warnings
warnings.filterwarnings("ignore")

notebook_dir = os.path.dirname(os.path.abspath("__file__"))
parent_dir = Path(notebook_dir).parent
os.chdir(parent_dir)
print(f"Current working directory: {os.getcwd()}")

settings = config.get_settings()
#%% md
## Tests
#%%
from optimizers.tax_optimizer import QTaxOptimizer

optimizer = QTaxOptimizer()
#%% md
### Tax loss harvesting
#%% md
#### cp.sum(z_cardinality) <= 3
#%%
weights = np.array([0.4, 0.4, 0.2, 0])
quantities = np.array([4, 2, 4, 0])
old_prices = np.array([100, 200, 50, 200])
new_prices = np.array([100, 100, 100, 100])
cov = np.eye(weights.shape[0])
cov[1, 3] = 0.92
cov[3, 1] = 0.92

new_quantities = optimizer.optimize_taxes(weights, quantities, old_prices, new_prices, cov, 1000)
#%% md
#### cp.sum(z_cardinality) <= 4
#%%
weights = np.array([0.4, 0.4, 0.2, 0])
quantities = np.array([4, 2, 4, 0])
old_prices = np.array([100, 200, 50, 200])
new_prices = np.array([100, 100, 100, 100])
cov = np.eye(weights.shape[0])
cov[1, 3] = 0.97
cov[3, 1] = 0.97

new_quantities = optimizer.optimize_taxes(weights, quantities, old_prices, new_prices, cov, 1000)
#%% md
### Other tests
#%%
weights = np.array([0.4, 0.4, 0.2])
quantities = np.array([4, 2, 4])
old_prices = np.array([100, 200, 50])
new_prices = np.array([100, 100, 100])
cov = np.eye(weights.shape[0])

new_quantities = optimizer.optimize_taxes(weights, quantities, old_prices, new_prices, cov, 1000)
#%%
weights = np.array([0.4, 0.4, 0.2])
quantities = np.array([4, 2, 4])
old_prices = np.array([100, 200, 50])
new_prices = np.array([100, 5, 50])
cov = np.eye(weights.shape[0])

new_quantities = optimizer.optimize_taxes(weights, quantities, old_prices, new_prices, cov, 1000)
#%%
weights = np.array([0.4, 0.4, 0.2])
quantities = np.array([4, 2, 4])
old_prices = np.array([100, 200, 50])
new_prices = np.array([100, 5, 50])
cov = np.eye(weights.shape[0])

new_quantities = optimizer.optimize_taxes(weights, quantities, old_prices, new_prices, cov, 1000)
#%%
weights = np.array([0.4, 0.4, 0.2])
quantities = np.array([4, 2, 4])
old_prices = np.array([100, 200, 50])
new_prices = np.array([100, 2000, 50])
cov = np.eye(weights.shape[0])

new_quantities = optimizer.optimize_taxes(weights, quantities, old_prices, new_prices, cov, 1000)
#%%
weights = np.array([0.4, 0.4, 0.2])
quantities = np.array([4, 2, 4])
old_prices = np.array([100, 200, 50])
new_prices = np.array([100, 100, 100])
cov = np.eye(weights.shape[0])

new_quantities = optimizer.optimize_taxes(weights, quantities, old_prices, new_prices, cov, 1000)
#%%
weights = np.array([0.4, 0.4, 0.2])
quantities = np.array([4, 2, 4])
old_prices = np.array([100, 200, 50])
new_prices = np.array([75, 450, 190])
cov = np.eye(weights.shape[0])

new_quantities = optimizer.optimize_taxes(weights, quantities, old_prices, new_prices, cov, 1000)
#%% md
### 5.8.2023.
#%%
investment_amount = 10000
investment_horizon = 1
date = datetime(2023, 8, 5)
#%%
ef_request = EfficientFrontierRequest(investmentAmount=investment_amount, investmentHorizon=investment_horizon, date=date)

ef_response: EfficientFrontierResponse = calculate_efficient_frontier(ef_request)
#%%
ef_response.frontier[50]
#%%
ef_response.covarianceMatrix
#%%
old_quantities = {col: 0 for col in isins}
weights = {col: 0 for col in isins}
#%%
for product in ef_response.frontier[50].products:
    old_quantities[product.isin] = product.quantity
    weights[product.isin] = product.weight
#%%
weights = np.array(list(weights.values()))
weights
#%%
old_quantities = np.array(list(old_quantities.values()))
old_quantities
#%%
old_prices, old_risk_free = etf_repository.EtfRepository().get_prices_eur(datetime(2023, 8, 5))
#%%
old_risk_free.T.reset_index(drop=True, inplace=True)
old_prices = pd.concat([old_prices, old_risk_free], axis=1)
#%%
old_prices
#%%
new_prices, new_risk_free = etf_repository.EtfRepository().get_prices_eur(datetime(2024, 8, 5))
#%%
new_risk_free.T.reset_index(drop=True, inplace=True)
new_prices = pd.concat([new_prices, new_risk_free], axis=1)
#%%
new_prices
#%%
new_quantities = optimizer.optimize_taxes(weights, old_quantities, np.array(old_prices), np.array(new_prices), ef_response.covarianceMatrix, investment_amount)
#%%
old_quantities
#%%
new_quantities
#%%
np.array(old_prices)
#%%
np.array(new_prices)
#%%
investment_amount - old_quantities @ np.array(old_prices).reshape(-1, 1)
#%%
cash = investment_amount - old_quantities @ np.array(old_prices).reshape(-1, 1)
new_weights = np.multiply(new_quantities.reshape(-1, 1), np.array(new_prices).reshape(-1, 1)) / (np.dot(old_quantities, np.array(new_prices).reshape(-1, 1)) + cash)
new_weights.reshape(1, -1)
#%%
pd.DataFrame(weights.reshape(1, -1))
#%% md
### 21.10.2021.
#%%
investment_amount = 10000
investment_horizon = 1
date = datetime(2021, 10, 21)
#%%
ef_request = EfficientFrontierRequest(investmentAmount=investment_amount, investmentHorizon=investment_horizon, date=date)

ef_response: EfficientFrontierResponse = calculate_efficient_frontier(ef_request)
#%%
ef_response.frontier[50]
#%%
ef_response.covarianceMatrix
#%%
old_quantities = {col: 0 for col in isins}
weights = {col: 0 for col in isins}
#%%
for product in ef_response.frontier[50].products:
    old_quantities[product.isin] = product.quantity
    weights[product.isin] = product.weight
#%%
weights = np.array(list(weights.values()))
weights
#%%
old_quantities = np.array(list(old_quantities.values()))
old_quantities
#%%
old_prices, old_risk_free = etf_repository.EtfRepository().get_prices_eur(datetime(2021, 10, 21))
#%%
old_risk_free.T.reset_index(drop=True, inplace=True)
old_prices = pd.concat([old_prices, old_risk_free], axis=1)
#%%
new_prices, new_risk_free = etf_repository.EtfRepository().get_prices_eur(datetime(2022, 10, 21))
#%%
new_risk_free.T.reset_index(drop=True, inplace=True)
new_prices = pd.concat([new_prices, new_risk_free], axis=1)
#%%
from optimizers.tax_optimizer import QTaxOptimizer

optimizer = QTaxOptimizer()
#%%
new_quantities = optimizer.optimize_taxes(weights, old_quantities, np.array(old_prices), np.array(new_prices), ef_response.covarianceMatrix, investment_amount)
#%%
old_quantities
#%%
new_quantities
#%%
np.array(old_prices)
#%%
np.array(new_prices)
#%%
np.array(new_prices) - np.array(old_prices)
#%%
investment_amount - old_quantities @ np.array(old_prices).reshape(-1, 1)
#%%
pd.DataFrame(weights.reshape(1, -1))
#%%
cash = investment_amount - old_quantities @ np.array(old_prices).reshape(-1, 1)
new_weights = np.multiply(new_quantities.reshape(-1, 1), np.array(new_prices).reshape(-1, 1)) / (np.dot(old_quantities, np.array(new_prices).reshape(-1, 1)) + cash)
new_weights.reshape(1, -1)