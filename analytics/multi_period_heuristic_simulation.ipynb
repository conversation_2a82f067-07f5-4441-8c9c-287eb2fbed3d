#%%
%load_ext autoreload
%autoreload 2
#%%
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import colormaps
from datetime import datetime

from optimizers.multi_period_optimizer import MultiPeriodOptimizer
from optimizers.efficient_frontier_optimizers import CapmOptimizer
from utils import simulation_utils
from models import benchmark_model
from configuration import config
from repositories.old_mongo_repositories import risk_free_rate_repository
#%%
cmap_20 = colormaps.get_cmap('tab20')
colors_20 = [cmap_20(i / 19) for i in range(20)]
#%%
settings = config.get_settings()

multi_period_optimizer = MultiPeriodOptimizer()
single_period_optimizer = CapmOptimizer()
regression_pricing_model = benchmark_model.RegressionPricingModel()
risk_free_rate_repository = risk_free_rate_repository.RiskFreeRateRepository()

last_date = datetime(2025, 2, 17)
#%%
avg_risk_free_rate = risk_free_rate_repository.get_avg_risk_free_rate_in_time_horizon(settings.time_horizon, last_date=last_date)
current_risk_free_rate = risk_free_rate_repository.get_last_risk_free_rate(last_date=last_date)

etf_returns, etf_covariance = regression_pricing_model.calculate_products_returns_and_covariance(avg_risk_free_rate=avg_risk_free_rate, current_risk_free_rate=current_risk_free_rate,
                                                                                                 last_date=last_date)
#%%
(single_period_efficient_frontier, 
 single_period_expected_returns,
 single_period_variances) = single_period_optimizer.calculate_efficient_frontier(returns=etf_returns, covariance=etf_covariance)
#%%
n_frontier_samples = 1
frontier_size = 100

sampled_efficient_frontier = single_period_efficient_frontier[::n_frontier_samples]
#%%
def calculate_strategy_returns(returns: np.ndarray, alphas: np.ndarray, n_periods: int, sampled_returns: np.ndarray = None) -> np.ndarray:
    assert returns.shape[1] == n_periods
    assert alphas.shape[0] == n_periods

    n_sims = returns.shape[0]
    starting_indices = np.arange(n_periods)
    ending_indices = np.arange(n_sims - n_periods + 1, n_sims + 1)

    sliced_returns = np.vstack([returns[start:end, i] for i, (start, end) in enumerate(zip(starting_indices, ending_indices))]).T

    if sampled_returns is not None:
        n_sampled = sampled_returns.shape[0]
        sliced_returns[:, :n_sampled] = sampled_returns

    log_returns = np.log1p(sliced_returns[:, ::-1])
    cumulative_log_returns = np.cumsum(log_returns, axis=1)[:, ::-1]
    cumulative_returns = np.expm1(cumulative_log_returns)

    portfolio_returns = cumulative_returns @ alphas
    
    # return cumulative_returns[:, 0].reshape(-1, 1)
    return portfolio_returns


def calculate_simulated_cumulative_return(simulated_returns: np.ndarray, alphas: np.ndarray):
    log_returns = np.log1p(simulated_returns[::-1])
    cumulative_log_returns = np.cumsum(log_returns)[::-1]
    cumulative_returns = np.expm1(cumulative_log_returns)

    portfolio_returns = cumulative_returns @ alphas

    # return cumulative_returns[0]
    return portfolio_returns


def is_sorted(array: np.ndarray, is_desc=True):
    return np.all(array[:-1] >= array[1:]) if is_desc else np.all(array[:-1] <= array[1:])


def approx_lte(x, y):
    return x <= y or np.isclose(x, y)


def calculate_CVaR(returns: np.ndarray, p: float):
    return -np.mean(np.sort(returns, axis=0)[:int(p * returns.shape[0])], axis=0)
#%%
def calculate_feasible_mp_strategies(current_strategy: np.ndarray, returns: np.ndarray, target_CVaR: float, alphas: np.ndarray, n_periods: int, p: float, current_portfolio: np.ndarray = None,
                                     sampled_returns: np.ndarray = None):
    n_portfolios = returns.shape[1]

    feasible_strategies = []
    feasible_strategies_expected_returns = []
    feasible_strategies_CVaRs = []

    starting_index = current_portfolio.shape[0] if current_portfolio is not None else 0

    for period_index in range(starting_index, n_periods):
        if current_strategy[period_index] >= n_portfolios - 1:
            continue

        new_strategy = current_strategy.copy()

        new_strategy[period_index] += 1

        if not is_sorted(new_strategy[starting_index:]):
            continue

        new_strategy_returns = calculate_strategy_returns(returns[:, new_strategy], alphas, n_periods, sampled_returns)
        new_strategy_expected_return = np.mean(new_strategy_returns, axis=0)
        new_strategy_CVaR = calculate_CVaR(new_strategy_returns, p)

        if not approx_lte(new_strategy_CVaR, target_CVaR):
            continue

        feasible_strategies.append(new_strategy)
        feasible_strategies_expected_returns.append(new_strategy_expected_return)
        feasible_strategies_CVaRs.append(new_strategy_CVaR)

    return np.array(feasible_strategies), np.array(feasible_strategies_expected_returns), np.array(feasible_strategies_CVaRs)
#%%
#TODO: create factory classes for calculating portfolio product_returns (our method, time-weighted), calculating return measure (expected return, median return, ...) and calculating risk measure (CVaR, VaR, volatility)

def multi_period_heuristic(sp_simulated_returns, target_CVaR, alphas, min_cvar_portfolio, p, current_portfolio: np.ndarray = None, sampled_returns: np.ndarray = None):
    n_periods = alphas.shape[0]

    current_strategy = np.repeat(min_cvar_portfolio, n_periods)

    if current_portfolio is not None:
        n_passed_periods = current_portfolio.shape[0]
        current_strategy[:n_passed_periods] = current_portfolio

    feasible_strategies, feasible_strategies_expected_returns, feasible_strategies_CVaRs = calculate_feasible_mp_strategies(current_strategy, sp_simulated_returns, target_CVaR, alphas, n_periods, p,
                                                                                                                            current_portfolio, sampled_returns)

    while feasible_strategies.size != 0:
        index_of_max_expected_return = np.argmax(feasible_strategies_expected_returns)
        current_strategy = feasible_strategies[index_of_max_expected_return]

        (feasible_strategies, 
         feasible_strategies_expected_returns, 
         feasible_strategies_CVaRs) = calculate_feasible_mp_strategies(current_strategy, 
                                                                       sp_simulated_returns, 
                                                                       target_CVaR, 
                                                                       alphas, 
                                                                       n_periods,
                                                                       p, 
                                                                       current_portfolio, 
                                                                       sampled_returns)

    current_strategy_returns = calculate_strategy_returns(sp_simulated_returns[:, current_strategy], alphas, n_periods, sampled_returns)
    current_strategy_expected_return = np.mean(current_strategy_returns)
    current_strategy_CVaR = calculate_CVaR(current_strategy_returns, p)

    return current_strategy, current_strategy_expected_return, current_strategy_CVaR
#%%
def multi_period_heuristic_2(sp_simulated_returns, target_CVaR, alphas, min_cvar_portfolio, p, current_portfolio: np.ndarray = None, sampled_returns: np.ndarray = None):
    n_periods = alphas.shape[0]
    n_portfolios = sp_simulated_returns.shape[1]

    current_strategy = np.repeat(n_portfolios - 1, n_periods)

    if current_portfolio is not None:
        n_passed_periods = current_portfolio.shape[0]
        current_strategy[:n_passed_periods] = current_portfolio

    feasible_strategies, feasible_strategies_expected_returns, feasible_strategies_CVaRs = calculate_feasible_mp_strategies_2(current_strategy, sp_simulated_returns, target_CVaR, alphas, n_periods, p,
                                                                                                                            current_portfolio, sampled_returns)

    while feasible_strategies.size != 0:
        index_of_min_risk = np.argmin(feasible_strategies_CVaRs)
        current_strategy = feasible_strategies[index_of_min_risk]

        (feasible_strategies, 
         feasible_strategies_expected_returns, 
         feasible_strategies_CVaRs) = calculate_feasible_mp_strategies_2(current_strategy, 
                                                                       sp_simulated_returns, 
                                                                       target_CVaR, 
                                                                       alphas, 
                                                                       n_periods,
                                                                       p, 
                                                                       current_portfolio, 
                                                                       sampled_returns)

    current_strategy_returns = calculate_strategy_returns(sp_simulated_returns[:, current_strategy], alphas, n_periods, sampled_returns)
    current_strategy_expected_return = np.mean(current_strategy_returns)
    current_strategy_CVaR = calculate_CVaR(current_strategy_returns, p)

    return current_strategy, current_strategy_expected_return, current_strategy_CVaR
#%%
def calculate_feasible_mp_strategies_2(current_strategy: np.ndarray, returns: np.ndarray, target_return: float, alphas: np.ndarray, n_periods: int, p: float, current_portfolio: np.ndarray = None,
                                     sampled_returns: np.ndarray = None):

    feasible_strategies = []
    feasible_strategies_expected_returns = []
    feasible_strategies_CVaRs = []

    starting_index = current_portfolio.shape[0] if current_portfolio is not None else 0

    for period_index in range(starting_index, n_periods):
        if current_strategy[period_index] == 0:
            continue

        new_strategy = current_strategy.copy()

        new_strategy[period_index] -= 1

        if not is_sorted(new_strategy[starting_index:]):
            continue

        new_strategy_returns = calculate_strategy_returns(returns[:, new_strategy], alphas, n_periods, sampled_returns)
        new_strategy_expected_return = np.mean(new_strategy_returns, axis=0)
        new_strategy_CVaR = calculate_CVaR(new_strategy_returns, p)

        if approx_lte(new_strategy_expected_return, target_return):
            continue

        feasible_strategies.append(new_strategy)
        feasible_strategies_expected_returns.append(new_strategy_expected_return)
        feasible_strategies_CVaRs.append(new_strategy_CVaR)

    return np.array(feasible_strategies), np.array(feasible_strategies_expected_returns), np.array(feasible_strategies_CVaRs)
#%%
n_periods = 3
n_samples = 50000
p = 0.05
#<editor-fold>

investments = np.arange(1, n_periods + 1)[::-1].reshape(-1, 1) * 1000
alphas = investments / np.sum(investments)
# alphas = np.ones(n_periods).reshape(-1, 1) / n_periods
# alphas = np.arange(n_periods, 0, -1).reshape(-1, 1) / np.sum(np.arange(n_periods, 0, -1))
# alphas = np.zeros((n_periods, 1))
# alphas[0] = 1
#%%
simulated_etf_returns = simulation_utils.sample_log_normal_from_arithmetic_parameters(etf_returns, etf_covariance, n_periods=1, n_samples=n_samples + n_periods)
#%%
simulated_sp_returns = simulation_utils.sample_log_normal_from_arithmetic_parameters(
    arithmetic_means=single_period_expected_returns,
    arithmetic_covariance=np.diag(single_period_variances),
    n_samples=n_samples+n_periods
)
#%%
simulated_sp_returns = simulated_etf_returns @ sampled_efficient_frontier.T
simulated_sp_returns.mean(axis=0)
#%%
simulated_sp_returns.var(axis=0)
#%%
all_sp_portfolios = [np.repeat(i, n_periods) for i in range(frontier_size)]

simulated_sp_ef_returns = np.hstack([calculate_strategy_returns(simulated_sp_returns[:, portfolio], alphas, n_periods) for portfolio in all_sp_portfolios])
sp_ef_expected_returns = np.mean(simulated_sp_ef_returns, axis=0)
sp_ef_vars = np.var(simulated_sp_ef_returns, axis=0)
sp_ef_expected_returns
#%%
simulated_sp_ef_returns.shape
#%%
sp_ef_cvar = - np.mean(np.sort(simulated_sp_ef_returns, axis=0)[:int(p * n_samples)], axis=0)
min_cvar_index = np.argmin(sp_ef_cvar)
plt.plot(sp_ef_cvar)
plt.show()
sp_ef_cvar

# </editor-fold>
#%%
max_neg_CVaR_index = np.argmax(sp_ef_cvar[sp_ef_cvar <= 0])
max_neg_CVaR_index
#%%
def simulate_mp_heuristic_investment(sp_ef_returns: np.ndarray, alphas: np.ndarray, target_CVaR: float, min_CVaR_index: int, p: float, investment_horizon: int):
    sp_ef_expected_returns = np.mean(sp_ef_returns, axis=0)
    sp_ef_variances = np.var(sp_ef_returns, axis=0)

    current_time_period = 0

    strategies = []
    strategy_expected_returns = []
    strategy_CVaRs = []
    simulated_returns = []
    current_portfolio = np.array([], dtype=int)

    while current_time_period < investment_horizon:
        sampled_returns = np.array(simulated_returns) if len(simulated_returns) > 0 else None
        current_strategy, current_strategy_expected_return, current_strategy_CVaR = multi_period_heuristic_2(sp_ef_returns,
                                                                                                           target_CVaR,
                                                                                                           alphas,
                                                                                                           min_CVaR_index,
                                                                                                           p,
                                                                                                           current_portfolio,
                                                                                                           sampled_returns)

        strategies.append(current_strategy)
        strategy_expected_returns.append(current_strategy_expected_return)
        strategy_CVaRs.append(current_strategy_CVaR)

        current_portfolio_index = current_strategy[current_time_period]
        current_portfolio = np.append(current_portfolio, current_portfolio_index)

        simulated_return = simulation_utils.sample_log_normal_from_arithmetic_parameters(
            arithmetic_means=sp_ef_expected_returns[current_portfolio_index],
            arithmetic_covariance=sp_ef_variances[current_portfolio_index],
            n_periods=1,
            n_samples=1
        ).item()
        
        simulated_returns.append(simulated_return)
        
        current_time_period += 1
    
    current_strategy_return = calculate_simulated_cumulative_return(simulated_returns, alphas)

    return strategies, strategy_expected_returns, strategy_CVaRs, simulated_returns, current_portfolio, current_strategy_return
#%%
def simulate_mp_heuristic_investment_multiple_times(sp_ef_returns: np.ndarray, alphas: np.ndarray, target_CVaR: float, min_CVaR_index: int, p: float, investment_horizon: int,
                                                    num_simulations: int = 100):
    all_strategies = []
    all_strategy_expected_returns = []
    all_strategy_CVaRs = []
    all_simulated_returns = []
    all_portfolios = []
    all_strategies_returns = []

    for _ in range(num_simulations):
        (strategies,
         strategy_expected_returns,
         strategy_CVaRs,
         simulated_returns,
         current_portfolio,
         current_strategy_return) = simulate_mp_heuristic_investment(sp_ef_returns, alphas, target_CVaR, min_CVaR_index, p, investment_horizon)

        all_strategies.append(strategies)
        all_strategy_expected_returns.append(strategy_expected_returns)
        all_strategy_CVaRs.append(strategy_CVaRs)
        all_simulated_returns.append(simulated_returns)
        all_portfolios.append(current_portfolio)
        all_strategies_returns.append(current_strategy_return)

    all_strategies = np.stack(all_strategies, axis=0)
    all_strategy_expected_returns = np.stack(all_strategy_expected_returns, axis=0)
    all_strategy_CVaRs = np.stack(all_strategy_CVaRs, axis=0)
    all_simulated_returns = np.stack(all_simulated_returns, axis=0)
    all_portfolios = np.stack(all_portfolios, axis=0)
    all_strategies_returns = np.stack(all_strategies_returns, axis=0)

    return all_strategies, all_strategy_expected_returns, all_strategy_CVaRs, all_simulated_returns, all_portfolios, all_strategies_returns

#%%
max_neg_CVaR_index = 60

all_strategies, all_strategy_expected_returns, all_strategy_CVaRs, all_simulated_returns, all_portfolios, all_strategies_returns = simulate_mp_heuristic_investment_multiple_times(
    simulated_sp_returns.copy(), alphas, sp_ef_expected_returns[max_neg_CVaR_index], 0, 0.05, n_periods, num_simulations=100)
#%%
current_strategy, current_strategy_expected_return, current_strategy_CVaR = multi_period_heuristic_2(simulated_sp_returns.copy(), sp_ef_expected_returns[max_neg_CVaR_index], alphas, 0, 0.05)
#%%
current_strategy
#%%
print(current_strategy_expected_return)
print(sp_ef_expected_returns[max_neg_CVaR_index])
#%%
print(current_strategy_CVaR)
print(sp_ef_vars[max_neg_CVaR_index])
#%%
print(np.var(all_strategies_returns))
print(np.var(simulated_sp_ef_returns[max_neg_CVaR_index]))
#%%
print(np.mean(all_strategies_returns))
print(sp_ef_expected_returns[max_neg_CVaR_index])
#%%
np.mean(all_portfolios, axis=0)
#%%
plt.hist(all_strategies_returns, density=True, label='Heuristic', bins=20)
plt.hist(simulated_sp_ef_returns[:, max_neg_CVaR_index], density=True, label='Naive', alpha=0.7, bins=20)
plt.axvline(sp_ef_expected_returns[max_neg_CVaR_index], color='r')
plt.legend()
plt.show()
print(calculate_CVaR(all_strategies_returns, p))
print(calculate_CVaR(simulated_sp_ef_returns[:, max_neg_CVaR_index], p))
print(simulated_sp_ef_returns[simulated_sp_ef_returns[:, max_neg_CVaR_index] < 0, max_neg_CVaR_index].shape[0] / n_samples)
#%%
print(np.median(all_strategies_returns))
print(np.median(simulated_sp_ef_returns[:, max_neg_CVaR_index]))
#%%
# np.save('./10_periods_005_CVaR/all_strategies_for_target_return.npy', all_strategies_for_target_return)
# np.save('./10_periods_005_CVaR/all_strategy_expected_returns.npy', all_strategy_expected_returns)
# np.save('./10_periods_005_CVaR/all_strategy_CVaRs.npy', all_strategy_CVaRs)
# np.save('./10_periods_005_CVaR/all_simulated_returns.npy', all_simulated_returns)
# np.save('./10_periods_005_CVaR/all_portfolios.npy', all_portfolios)
# np.save('./10_periods_005_CVaR/all_strategies_returns.npy', all_strategies_returns)
#%%
# all_portfolios
#%%
# print(all_portfolios[42])
# print(all_portfolios[48])
#%%
# print(all_simulated_returns[42].tolist())
# print(all_simulated_returns[48])
#%%
# print(all_strategies_returns[42])
# print(all_strategies_returns[48])