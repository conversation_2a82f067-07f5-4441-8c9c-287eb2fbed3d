#%%
%load_ext autoreload
%autoreload 2
#%%
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from itertools import *

from optimizers.multi_period_optimizer import MultiPeriodOptimizer
from optimizers.efficient_frontier_optimizers import CapmOptimizer
from utils import simulation_utils
from models import benchmark_model
from configuration import config
from repositories.old_mongo_repositories import risk_free_rate_repository
#%%
settings = config.get_settings()

multi_period_optimizer = MultiPeriodOptimizer()
single_period_optimizer = CapmOptimizer()
pricing_model = benchmark_model.RegressionPricingModel()
risk_free_rate_repository = risk_free_rate_repository.RiskFreeRateRepository()

last_date = datetime(2025, 2, 17)
#%%
avg_risk_free_rate = risk_free_rate_repository.get_avg_risk_free_rate_in_time_horizon(settings.time_horizon, last_date=last_date)
current_risk_free_rate = risk_free_rate_repository.get_last_risk_free_rate(last_date=last_date)

etf_returns, etf_covariance = pricing_model.calculate_products_returns_and_covariance(avg_risk_free_rate=avg_risk_free_rate, current_risk_free_rate=current_risk_free_rate, last_date=last_date)
#%%
etf_returns.T
#%%
etf_covariance
#%%
(single_period_efficient_frontier,
 single_period_returns,
 single_period_variances) = single_period_optimizer.calculate_efficient_frontier(product_returns=etf_returns, product_covariance=etf_covariance)
#%%
n_frontier_samples = 1
frontier_size = 100

n_periods = 2
n_samples = 10000 + n_periods

sampled_efficient_frontier = single_period_efficient_frontier[::n_frontier_samples]
sampled_ef_returns = single_period_returns[::n_frontier_samples]
sampled_ef_variances = single_period_variances[::n_frontier_samples]
#%%
simulated_etf_returns = simulation_utils.sample_log_normal_from_arithmetic_parameters(etf_returns, etf_covariance, n_periods=1, n_samples=n_samples)
#%%
single_period_returns[::n_frontier_samples].T
#%%
single_period_variances[::n_frontier_samples].T
#%%
print(simulated_etf_returns.shape)
print(sampled_efficient_frontier.T.shape)
#%%
simulated_frontier_returns = simulated_etf_returns @ sampled_efficient_frontier.T
print(simulated_frontier_returns.shape)
simulated_frontier_returns.mean(axis=0)
#%%
def calculate_pairwise_returns(returns: np.ndarray, alphas: np.ndarray, n_periods: int) -> (np.ndarray, np.ndarray):
    assert returns.shape[1] == n_periods
    assert alphas.shape[0] == n_periods

    n_sims = returns.shape[0]

    cumulative_returns_without_rebalance = np.zeros((n_sims - 1, 1))
    cumulative_returns_with_rebalance = np.zeros((n_sims - 1, 1))

    for t in range(n_sims + 1 - n_periods):
        indices = np.arange(n_periods)
        asset_cumulative_returns_with_rebalance = returns[t + indices, indices]
        cumulative_returns_with_rebalance[t] = ((np.cumprod(1 + asset_cumulative_returns_with_rebalance[::-1]) - 1)[::-1] @ alphas).item()

        asset_cumulative_returns_without_rebalance = np.zeros(n_periods)
        for i in range(n_periods):
            asset_cumulative_returns_without_rebalance[i] = np.cumprod(1 + returns[t + i: t + n_periods, i])[-1] - 1

        cumulative_returns_without_rebalance[t] = (asset_cumulative_returns_without_rebalance @ alphas).item()

    return cumulative_returns_with_rebalance, cumulative_returns_without_rebalance


def get_diagonal(matrix: np.ndarray) -> np.ndarray:
    return np.einsum(''.join(['i' for _ in range(matrix.ndim)]) + '->i', matrix)
#%%
n_periods = 2
alphas = np.ones((n_periods, 1)) / n_periods
#%%
frontier_mus = np.ones((frontier_size,) * n_periods) * -1
frontier_sigmas = np.ones((frontier_size,) * n_periods) * -1
frontier_medians = np.ones((frontier_size,) * n_periods) * -1
frontier_VaRs = np.ones((frontier_size,) * n_periods) * -1
rebalance_mus = np.ones((frontier_size,) * n_periods) * -1
rebalance_sigmas = np.ones((frontier_size,) * n_periods) * -1
rebalance_medians = np.ones((frontier_size,) * n_periods) * -1
rebalance_VaRs = np.ones((frontier_size,) * n_periods) * -1
#%%
for index_tuple in product(range(frontier_size), repeat=n_periods):
    pairwise_returns_with_rebalance, pairwise_returns_without_rebalance = calculate_pairwise_returns(simulated_frontier_returns[:, index_tuple], alphas, n_periods)

    rebalance_mus[index_tuple] = pairwise_returns_with_rebalance.mean()
    rebalance_sigmas[index_tuple] = pairwise_returns_with_rebalance.std()
    rebalance_medians[index_tuple] = np.median(pairwise_returns_with_rebalance)
    rebalance_VaRs[index_tuple] = -np.percentile(pairwise_returns_with_rebalance, 5)

    frontier_mus[index_tuple] = pairwise_returns_without_rebalance.mean()
    frontier_sigmas[index_tuple] = pairwise_returns_without_rebalance.std()
    frontier_medians[index_tuple] = np.median(pairwise_returns_without_rebalance)
    frontier_VaRs[index_tuple] = -np.percentile(pairwise_returns_without_rebalance, 5)
#%%
def find_efficient_frontier(frontier_mus, frontier_medians, frontier_sigmas, frontier_VaRs, rb_mus, rb_medians, rb_sigmas, rb_VaRs, n_portfolios, n_periods, sort_by_VaR=False):
    efficient_frontier_mus = np.zeros(n_portfolios)
    efficient_frontier_sigmas = np.zeros(n_portfolios)
    efficient_frontier_medians = np.zeros(n_portfolios)
    efficient_frontier_VaRs = np.zeros(n_portfolios)
    ef_indices = np.zeros((n_portfolios, n_periods)).astype(int)

    rebalance_mus = np.zeros(n_portfolios)
    rebalance_sigmas = np.zeros(n_portfolios)
    rebalance_medians = np.zeros(n_portfolios)
    rebalance_VaRs = np.zeros(n_portfolios)
    rb_indices = np.zeros((n_portfolios, n_periods)).astype(int)
    
    sorting_risk = frontier_VaRs if sort_by_VaR else frontier_sigmas
    sorting_risk_rebalance = rb_VaRs if sort_by_VaR else rb_sigmas

    for sp_index, sp_risk in enumerate(get_diagonal(sorting_risk)):
        less_risky_indices = np.where(sorting_risk <= sp_risk)
        less_risky_indices_rebalance = np.where(sorting_risk_rebalance <= sp_risk)

        max_return_indices = np.argmax(frontier_mus[less_risky_indices])
        max_return_index = tuple(idx[max_return_indices] for idx in less_risky_indices)

        max_return_indices_rebalance = np.argmax(rb_mus[less_risky_indices_rebalance])
        max_return_index_rebalance = tuple(idx[max_return_indices_rebalance] for idx in less_risky_indices_rebalance)

        efficient_frontier_mus[sp_index] = frontier_mus[max_return_index]
        efficient_frontier_medians[sp_index] = frontier_medians[max_return_index]
        efficient_frontier_sigmas[sp_index] = frontier_sigmas[max_return_index]
        efficient_frontier_VaRs[sp_index] = frontier_VaRs[max_return_index]
        ef_indices[sp_index] = max_return_index

        rebalance_mus[sp_index] = rb_mus[max_return_index_rebalance]
        rebalance_medians[sp_index] = rb_medians[max_return_index_rebalance]
        rebalance_sigmas[sp_index] = rb_sigmas[max_return_index_rebalance]
        rebalance_VaRs[sp_index] = rb_VaRs[max_return_index_rebalance]
        rb_indices[sp_index] = max_return_index_rebalance

    return efficient_frontier_mus, efficient_frontier_medians, efficient_frontier_sigmas, efficient_frontier_VaRs, ef_indices, rebalance_mus, rebalance_medians, rebalance_sigmas, rebalance_VaRs, rb_indices
#%% md
# Sorted by sigmas
#%%
(efficient_frontier_mus,
 efficient_frontier_medians,
 efficient_frontier_sigmas,
 efficient_frontier_VaRs,
 efficient_frontier_indices,
 ef_rebalance_mus,
 ef_rebalance_medians,
 ef_rebalance_sigmas,
 ef_rebalance_VaRs,
 ef_rebalance_indices) = find_efficient_frontier(frontier_mus,
                                                 frontier_medians,
                                                 frontier_sigmas,
                                                 frontier_VaRs,
                                                 rebalance_mus,
                                                 rebalance_medians,
                                                 rebalance_sigmas,
                                                 rebalance_VaRs,
                                                 frontier_size,
                                                 n_periods)
#%%
print(get_diagonal(efficient_frontier_mus))
#%%
print(efficient_frontier_indices)
#%%
plt.plot(sampled_ef_variances[efficient_frontier_indices][1:-1].T)
plt.show()
#%%
print(ef_rebalance_indices)
#%%
plt.plot(sampled_ef_variances[ef_rebalance_indices][1:-1].T)
plt.show()
#%%
fig, ax = plt.subplots(figsize=(10, 10))
ax.plot(get_diagonal(frontier_sigmas), get_diagonal(frontier_mus), label="Diagonal", color='blue')
ax.plot(efficient_frontier_sigmas, efficient_frontier_mus, label="Combinations", color='red')
ax.plot(ef_rebalance_sigmas, ef_rebalance_mus, label="Combinations with reinvesting", color='orange')
ax.set_xlabel('Sigma')
ax.set_ylabel('Mu')
plt.title(f'Mu - Sigma for {n_periods} periods')
plt.legend()
plt.savefig(f'plots/sigma/Mu-Sigma_for_{n_periods}_periods_{frontier_size}_portfolios.png')
plt.show()
#%%
fig, ax = plt.subplots(figsize=(10, 10))
ax.plot(get_diagonal(frontier_VaRs), get_diagonal(frontier_mus), label="Diagonal", color='blue')
ax.plot(efficient_frontier_VaRs, efficient_frontier_mus, label="Combinations", color='red')
ax.plot(ef_rebalance_VaRs, ef_rebalance_mus, label="Combinations with rebalance", color='orange')
ax.set_xlabel('VaR')
ax.set_ylabel('Mu')
plt.title(f'Mu - VaR for {n_periods} periods')
plt.legend()
plt.savefig(f'plots/sigma/Mu-Sigma_for_{n_periods}_periods_{frontier_size}_portfolios.png')
plt.show()
#%%
fig, ax = plt.subplots(figsize=(10, 10))
ax.plot(get_diagonal(frontier_VaRs), get_diagonal(frontier_medians), label="Diagonal", color='blue')
ax.plot(efficient_frontier_VaRs, efficient_frontier_medians, label="Combinations", color='red')
ax.plot(ef_rebalance_VaRs, ef_rebalance_medians, label="Combinations with rebalance", color='orange')
ax.set_xlabel('VaR')
ax.set_ylabel('Median')
plt.title(f'Median - VaR for {n_periods} periods')
plt.legend()
plt.savefig(f'plots/sigma/Median-VaR_for_{n_periods}_periods_{frontier_size}_portfolios.png')
plt.show()
#%% md
# Sorted by VaRs
#%%
(efficient_frontier_mus,
 efficient_frontier_medians,
 efficient_frontier_sigmas,
 efficient_frontier_VaRs,
 efficient_frontier_indices,
 ef_rebalance_mus,
 ef_rebalance_medians,
 ef_rebalance_sigmas,
 ef_rebalance_VaRs,
 ef_rebalance_indices) = find_efficient_frontier(frontier_mus,
                                                 frontier_medians,
                                                 frontier_sigmas,
                                                 frontier_VaRs,
                                                 rebalance_mus,
                                                 rebalance_medians,
                                                 rebalance_sigmas,
                                                 rebalance_VaRs,
                                                 frontier_size,
                                                 n_periods,
                                                 sort_by_VaR=True)
#%%
print(efficient_frontier_indices)
#%%
plt.plot(sampled_ef_variances[efficient_frontier_indices][1:-1].T)
plt.show()
#%%
print(ef_rebalance_indices)
#%%
plt.plot(sampled_ef_variances[ef_rebalance_indices][1:-1].T)
plt.show()
#%%
fig, ax = plt.subplots(figsize=(10, 10))
ax.plot(get_diagonal(frontier_sigmas), get_diagonal(frontier_mus), label="Diagonal", color='blue')
ax.plot(efficient_frontier_sigmas, efficient_frontier_mus, label="Combinations", color='red')
ax.plot(ef_rebalance_sigmas, ef_rebalance_mus, label="Combinations with reinvesting", color='orange')
ax.set_xlabel('Sigma')
ax.set_ylabel('Mu')
plt.title(f'Mu - Sigma for {n_periods} periods')
plt.legend()
plt.savefig(f'plots/var/Mu-Sigma_for_{n_periods}_periods_{frontier_size}_portfolios.png')
plt.show()
#%%
fig, ax = plt.subplots(figsize=(10, 10))
ax.plot(get_diagonal(frontier_VaRs), get_diagonal(frontier_mus), label="Diagonal", color='blue')
ax.plot(efficient_frontier_VaRs, efficient_frontier_mus, label="Combinations", color='red')
ax.plot(ef_rebalance_VaRs, ef_rebalance_mus, label="Combinations with rebalance", color='orange')
ax.set_xlabel('VaR')
ax.set_ylabel('Mu')
plt.title(f'Mu - VaR for {n_periods} periods')
plt.legend()
plt.savefig(f'plots/var/Mu-Sigma_for_{n_periods}_periods_{frontier_size}_portfolios.png')
plt.show()
#%%
fig, ax = plt.subplots(figsize=(10, 10))
ax.plot(get_diagonal(frontier_VaRs), get_diagonal(frontier_medians), label="Diagonal", color='blue')
ax.plot(efficient_frontier_VaRs, efficient_frontier_medians, label="Combinations", color='red')
ax.plot(ef_rebalance_VaRs, ef_rebalance_medians, label="Combinations with rebalance", color='orange')
ax.set_xlabel('VaR')
ax.set_ylabel('Median')
plt.title(f'Median - VaR for {n_periods} periods')
plt.legend()
plt.savefig(f'plots/var/Median-VaR_for_{n_periods}_periods_{frontier_size}_portfolios.png')
plt.show()