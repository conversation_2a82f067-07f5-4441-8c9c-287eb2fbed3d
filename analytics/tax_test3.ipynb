#%%
from optimizers.efficient_frontier_optimizer import CapmLpOptimizer
from utils.multi_period_analytics_utils import simulate_investment
%load_ext autoreload
%autoreload 2
#%%
import os
from pathlib import Path
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

from configuration import config
from requests_.requests import EfficientFrontierRequest, BacktestRequest
from responses_.responses import EfficientFrontierResponse
from main import calculate_efficient_frontier
from repositories.old_mongo_repositories import etf_repository
from optimizers.efficient_frontier_optimizer import CapmLpOptimizer
#%%
import warnings

warnings.filterwarnings("ignore")

notebook_dir = os.path.dirname(os.path.abspath("__file__"))
parent_dir = Path(notebook_dir).parent
os.chdir(parent_dir)
print(f"Current working directory: {os.getcwd()}")

settings = config.get_settings()
#%%
isins = ['IE00B4L5Y983', 'IE00B5BMR087', 'IE00BJ0KDR00', 'IE00B4K48X80', 'LU1781541252', 'IE00BTJRMP35', 'IE00BF4RFH31', 'IE00BGSF1X88', 'IE00BYXPSP02',
         'IE00B3VWN518', 'IE00B1FZSC47', 'IE00BYXYYJ35', 'IE00BF11F565', 'IE00BYXYYL56', 'LU0290355717', 'DE000A0S9GB0', 'LU1437018838', 'FR0010510800']
# isins = ['IE00BJ0KDR00', 'IE00B4K48X80', 'LU1781541252', 'IE00BTJRMP35', 'IE00BF4RFH31', 'IE00BGSF1X88', 'IE00BYXPSP02',
#          'IE00B3VWN518', 'IE00B1FZSC47', 'IE00BYXYYJ35', 'IE00BF11F565', 'IE00BYXYYL56', 'LU0290355717', 'IE00B579F325', 'LU1437018838', 'FR0010510800']
#%%
len(isins)
#%%
from utils.pydantic_models import PurchaseInfo
from optimizers.rebalance_engine import RebalanceEngine, AssetTransactions, Transactions
from utils import simulation_utils
from typing import Optional
import copy

optimizer = RebalanceEngine()
#%%
investment_amount = 10000
investment_horizon = 10
date = datetime(2025, 2, 3)
#%%
ef_request = EfficientFrontierRequest(investmentAmount=investment_amount, investmentHorizon=investment_horizon, calculateOnDatetime=date)

ef_response: EfficientFrontierResponse = calculate_efficient_frontier(ef_request)
#%%
cov = np.array(ef_response.covarianceMatrix)
cov[-1, -1] = 0.05
#%%
def plot_cost_te(x_values, y_values):
    plt.figure(figsize=(10, 8))
    plt.plot(x_values, y_values, marker='o', color='blue', linewidth=2, linestyle='--')
    plt.xlabel("trade cost", fontsize=12)
    plt.ylabel("tracking error", fontsize=12)
    plt.grid(alpha=0.5)
    plt.show()
#%%
def plot_cost_mean(x_values, y_values):
    plt.figure(figsize=(10, 8))
    plt.plot(x_values, y_values, marker='o', color='blue', linewidth=2, linestyle='--')
    plt.axhline(y=0.06276254138153474, color='r', linestyle='--')
    plt.xlabel("trade cost", fontsize=12)
    plt.ylabel("mean", fontsize=12)
    plt.grid(alpha=0.5)
    plt.show()
#%%
def plot_cost_variance(x_values, y_values):
    plt.figure(figsize=(10, 8))
    plt.plot(x_values, y_values, marker='o', color='blue', linewidth=2, linestyle='--')
    plt.axhline(y=0.005950886015327254, color='r', linestyle='--')
    plt.xlabel("trade cost", fontsize=12)
    plt.ylabel("variance", fontsize=12)
    plt.grid(alpha=0.5)
    plt.show()
#%%
def calculate_slopes(x_values, y_values):
    x_values = np.array(x_values)
    y_values = np.array(y_values)

    slopes = (y_values[1:] - y_values[:-1]) / (x_values[1:] - x_values[:-1])
    return slopes
#%%
def find_elbow_old(x_values, y_values):
    unique_x = {}
    for x, y in zip(x_values, y_values):
        unique_x[x] = y

    x_values = np.array(sorted(unique_x.keys()))
    y_values = np.array([unique_x[x] for x in x_values])

    if len(x_values) == 2:
        return x_values[1]

    scale_x = max(x_values) - min(x_values)
    scale_y = max(y_values) - min(y_values)

    x_norm = (x_values - min(x_values)) / scale_x
    y_norm = (y_values - min(y_values)) / scale_y

    dx = np.diff(x_norm)
    dy = np.diff(y_norm)

    angle_rad = np.arctan2(dy, dx)

    angles_diff = np.diff(angle_rad)

    elbow_index = np.argmax(angles_diff) + 1

    # print(angle_rad)
    # print(angles_diff)

    return x_values[elbow_index]
#%%
#TODO get scale_x and scale_y in run_tax_optimizer, i need to call this method every time and figure out an early stop

def find_elbow(x_values, y_values, scale_x, scale_y):
    unique_x = {}
    for x, y in zip(x_values, y_values):
        unique_x[x] = y

    x_values = np.array(sorted(unique_x.keys()))
    y_values = np.array([unique_x[x] for x in x_values])

    if len(x_values) == 2:
        return x_values[1]

    x_norm = (x_values - min(x_values)) / scale_x
    y_norm = (y_values - min(y_values)) / scale_y

    dx = np.diff(x_values)
    dy = np.diff(y_values)

    angle_rad = np.arctan2(dy, dx)

    angles_diff = np.diff(angle_rad)

    elbow_index = np.argmax(angles_diff) + 1

    return x_values[elbow_index]
#%%
etf_repo = etf_repository.EtfRepository()
#%%
ef_response.frontier[ef_response.optimalIndex]
#%%
old_quantities = {col: 0 for col in isins}
old_weights = {col: 0 for col in isins}

for product in ef_response.frontier[ef_response.optimalIndex].products:
    old_quantities[product.isin] = product.quantity
    old_weights[product.isin] = product.weight

old_weights = np.array(list(old_weights.values()))
old_quantities = np.array(list(old_quantities.values()))
old_prices, old_risk_free = etf_repository.EtfRepository().get_prices_eur(date)
old_risk_free.T.reset_index(drop=True, inplace=True)
old_prices = pd.concat([old_prices, old_risk_free], axis=1).values[0]
#%%
old_quantities
#%%
old_prices
#%%
ef = pd.read_csv("efficient_frontier.csv")
#%%
weights = np.array(ef.iloc[57])
#%%
(old_quantities @ old_prices.T).item()
#%%
assets = [
    AssetTransactions(transactions = [PurchaseInfo(quantity=quantity, purchase_date=date, price=price, trading_cost=0)] if quantity > 0 else [])
    for quantity, price in zip(old_quantities, old_prices)
]

transactions = Transactions(asset_transactions= assets)
#%%
print(transactions)
#%%
capm_optimizer = CapmLpOptimizer()
#%%
returns = np.array(ef_response.annualReturns)
covariance = np.array(ef_response.covarianceMatrix)
#%%
covariance
#%%
(single_period_efficient_frontier) = capm_optimizer.calculate_efficient_frontier(returns=returns, covariance=covariance)
#%%
single_period_efficient_frontier
#%%
weights = single_period_efficient_frontier[55, :]
#%%
weights
#%%
def run_tax_optimizer_with_elbow(weights,
                      transactions,
                      new_prices,
                      covariance,
                      cash,
                      investment_horizon,
                      years_left,
                      allowed_trade_cost: Optional[float] = None,
                      allowed_capital_gains: Optional[float] = None):
    date = datetime.today()
    date = date.replace(year=date.year + investment_horizon - years_left + 1)

    (new_quantities_opt,
     tracking_error_opt,
     new_cash_opt,
     portfolio_value_opt,
     c_g_opt,
     t_c_opt) = optimizer.rebalance_portfolio(weights, transactions, new_prices, covariance, cash, allowed_trade_cost=allowed_trade_cost, allowed_capital_gains=allowed_capital_gains)

    quantities = transactions.get_quantities().flatten()

    if t_c_opt == 0:
        return transactions, new_cash_opt, portfolio_value_opt, c_g_opt, t_c_opt, tracking_error_opt, 0

    all_quantities = []
    all_tracking_errors = []
    all_cash = []
    all_portfolio_values = []
    all_capital_gains = []
    all_trade_costs = []

    trade_costs = list(range(int(np.ceil(t_c_opt)) + 1))

    for allowed_trade_cost in trade_costs:
        (new_quantities, tracking_error, new_cash, portfolio_value, capital_gains, trade_cost) = optimizer.rebalance_portfolio(weights, transactions, new_prices, covariance, cash,
                                                                                                                               allowed_trade_cost=allowed_trade_cost,
                                                                                                                               allowed_capital_gains=allowed_capital_gains)

        all_quantities.append(new_quantities)
        all_tracking_errors.append(tracking_error)
        all_cash.append(new_cash)
        all_portfolio_values.append(portfolio_value)
        all_capital_gains.append(capital_gains)
        all_trade_costs.append(trade_cost)

    # print(t_c_opt, tracking_error_opt)
    # print(all_trade_costs)
    # print(all_tracking_errors)

    optimal_cost = find_elbow_old(all_trade_costs, all_tracking_errors)
    # print(optimal_cost, find_elbow(all_trade_costs, all_tracking_errors))
    optimal_cost_index = all_trade_costs.index(optimal_cost)

    # print("################")
    # print(optimal_cost, optimal_cost_index)
    # print("################")

    # plt.plot(all_trade_costs, all_tracking_errors)
    # plt.show()

    elbow_quantities = all_quantities[optimal_cost_index]
    elbow_tracking_error = all_tracking_errors[optimal_cost_index]
    elbow_cash = all_cash[optimal_cost_index]
    elbow_portfolio_value = all_portfolio_values[optimal_cost_index]
    elbow_capital_gains = all_capital_gains[optimal_cost_index]
    elbow_trade_cost = all_trade_costs[optimal_cost_index]

    delta_quantities = elbow_quantities - quantities

    trade_costs_diff = all_trade_costs[-1] - t_c_opt

    for delta, asset_transactions, new_price in zip(delta_quantities, transactions.asset_purchase_lots, new_prices):
        if delta > 0:
            asset_transactions.purchase_lots.append(PurchaseInfo(quantity=delta, purchase_date=date, price=new_price, trading_cost=0))
        elif delta < 0:
            for transaction in asset_transactions.purchase_lots[:]:
                if transaction.quantity <= -delta:
                    delta += transaction.quantity
                    asset_transactions.purchase_lots.remove(transaction)
                else:
                    transaction.quantity += delta

    return transactions, elbow_cash, elbow_portfolio_value, elbow_capital_gains, elbow_trade_cost, elbow_tracking_error, trade_costs_diff
#%%
from joblib import Parallel, delayed, parallel_backend

def run_tax_optimizer_with_elbow_par(weights,
                                  transactions,
                                  new_prices,
                                  covariance,
                                  cash,
                                  investment_horizon,
                                  years_left,
                                  allowed_trade_cost: Optional[float] = None,
                                  allowed_capital_gains: Optional[float] = None):

    date = datetime.today()
    date = date.replace(year=date.year + investment_horizon - years_left + 1)

    # Step 1: Get the optimal solution
    (new_quantities_opt,
     tracking_error_opt,
     new_cash_opt,
     portfolio_value_opt,
     c_g_opt,
     t_c_opt) = optimizer.rebalance_portfolio(weights, transactions, new_prices, covariance, cash, allowed_trade_cost=allowed_trade_cost, allowed_capital_gains=allowed_capital_gains)

    quantities = transactions.get_quantities().flatten()

    if t_c_opt == 0:
        return transactions, new_cash_opt, portfolio_value_opt, c_g_opt, t_c_opt, tracking_error_opt, 0

    trade_costs = list(range(int(np.ceil(t_c_opt)) + 1))

    with parallel_backend("loky", n_jobs=4):
        results = Parallel()(
            delayed(optimizer.rebalance_portfolio)(
                weights, transactions, new_prices, covariance, cash,
                date=date,
                allowed_trade_cost=cost,
                allowed_capital_gains=allowed_capital_gains
            )
            for cost in trade_costs
        )

    all_quantities, all_tracking_errors, all_cash, all_portfolio_values, all_capital_gains, all_trade_costs = zip(*results)

    optimal_cost = find_elbow_old(all_trade_costs, all_tracking_errors)
    optimal_cost_index = all_trade_costs.index(optimal_cost)

    print(optimal_cost)

    elbow_quantities = all_quantities[optimal_cost_index]
    elbow_tracking_error = all_tracking_errors[optimal_cost_index]
    elbow_cash = all_cash[optimal_cost_index]
    elbow_portfolio_value = all_portfolio_values[optimal_cost_index]
    elbow_capital_gains = all_capital_gains[optimal_cost_index]
    elbow_trade_cost = all_trade_costs[optimal_cost_index]

    delta_quantities = elbow_quantities - quantities
    trade_costs_diff = max(all_trade_costs) - t_c_opt

    for delta, asset_transactions, new_price in zip(delta_quantities, transactions.asset_purchase_lots, new_prices):
        if delta > 0:
            asset_transactions.purchase_lots.append(
                PurchaseInfo(quantity=delta, purchase_date=date, price=new_price, trading_cost=0)
            )
        elif delta < 0:
            for transaction in asset_transactions.purchase_lots[:]:
                if transaction.quantity <= -delta:
                    delta += transaction.quantity
                    asset_transactions.purchase_lots.remove(transaction)
                else:
                    transaction.quantity += delta
                    break

    return transactions, elbow_cash, elbow_portfolio_value, elbow_capital_gains, elbow_trade_cost, elbow_tracking_error, trade_costs_diff
#%% md
We need three methods:
 - run_tax_optimizer
    - it needs to calculate tax optimisation for each lambda and find elbow
    - transactions will be updated with elbows delta_quantites

 - simulate_investment
    - it will iterate through time horizon periods and for each period:
        - simulate returns and calculate new prices
        - call run_tax_optimizer

- simulate_investment_multiple_times
    - call simulate_investment n_sims times
#%%
def run_tax_optimizer(weights,
                      transactions,
                      new_prices,
                      covariance,
                      cash,
                      investment_horizon,
                      years_left,
                      allowed_trade_cost: Optional[float] = None,
                      allowed_capital_gains: Optional[float] = None):
    date = datetime.today()
    date = date.replace(year=date.year + investment_horizon - years_left + 1)

    (new_quantities_opt,
     tracking_error_opt,
     new_cash_opt,
     portfolio_value_opt,
     c_g_opt,
     t_c_opt) = optimizer.rebalance_portfolio(weights, transactions, new_prices, covariance, cash, allowed_trade_cost=allowed_trade_cost, allowed_capital_gains=allowed_capital_gains)

    quantities = transactions.get_quantities().flatten()

    # if t_c_opt == 0:
    #     return purchase_lots, new_cash_opt, portfolio_value_opt, c_g_opt, t_c_opt, tracking_error_opt, 0
    #
    # all_quantities = []
    # all_tracking_errors = []
    # all_cash = []
    # all_portfolio_values = []
    # all_capital_gains = []
    # all_trade_costs = []
    #
    # trade_costs = list(range(int(np.ceil(t_c_opt)) + 1))
    #
    # for allowed_trade_cost in trade_costs:
    #     (new_quantities, tracking_error, new_cash, portfolio_value, capital_gains, trade_cost) = optimizer.rebalance_portfolio(weights, purchase_lots, new_prices, covariance, cash, date=date,
    #                                                                                                                            allowed_trade_cost=allowed_trade_cost,
    #                                                                                                                            allowed_capital_gains=allowed_capital_gains)
    #
    #     all_quantities.append(new_quantities)
    #     all_tracking_errors.append(tracking_error)
    #     all_cash.append(new_cash)
    #     all_portfolio_values.append(portfolio_value)
    #     all_capital_gains.append(capital_gains)
    #     all_trade_costs.append(trade_cost)
    #
    # # print(t_c_opt, tracking_error_opt)
    # # print(all_trade_costs)
    # # print(all_tracking_errors)
    #
    # optimal_cost = find_elbow(all_trade_costs, all_tracking_errors)
    # optimal_cost_index = all_trade_costs.index(optimal_cost)
    #
    # # print("################")
    # # print(optimal_cost, optimal_cost_index)
    # # print("################")
    #
    # # plt.plot(all_trade_costs, all_tracking_errors)
    # # plt.show()
    #
    # elbow_quantities = all_quantities[optimal_cost_index]
    # elbow_tracking_error = all_tracking_errors[optimal_cost_index]
    # elbow_cash = all_cash[optimal_cost_index]
    # elbow_portfolio_value = all_portfolio_values[optimal_cost_index]
    # elbow_capital_gains = all_capital_gains[optimal_cost_index]
    # elbow_trade_cost = all_trade_costs[optimal_cost_index]
    #
    # delta_quantities = elbow_quantities - quantities
    #
    # trade_costs_diff = all_trade_costs[-1] - t_c_opt

    delta_quantities = new_quantities_opt - quantities

    trade_costs_diff = 0

    for delta, asset_transactions, new_price in zip(delta_quantities, transactions.asset_purchase_lots, new_prices):
        if delta > 0:
            asset_transactions.purchase_lots.append(PurchaseInfo(quantity=delta, purchase_date=date, price=new_price, trading_cost=0))
        elif delta < 0:
            for transaction in asset_transactions.purchase_lots[:]:
                if transaction.quantity <= -delta:
                    delta += transaction.quantity
                    asset_transactions.purchase_lots.remove(transaction)
                else:
                    transaction.quantity += delta

    # return purchase_lots, elbow_cash, elbow_portfolio_value, elbow_capital_gains, elbow_trade_cost, elbow_tracking_error, trade_costs_diff
    return transactions, new_cash_opt, portfolio_value_opt, c_g_opt, t_c_opt, tracking_error_opt, trade_costs_diff
#%%
def simulate_investment(returns: np.ndarray,
                        covariance: np.ndarray,
                        target_weights: np.ndarray,
                        old_prices: np.ndarray,
                        transactions: Transactions,
                        invested_wealth: float,
                        investment_horizon: int):
    simulated_returns = []

    period_wealths = []
    capital_gains = []
    trade_costs = []
    tracking_errors = []
    cash_amounts = []
    portfolio_returns = []
    trade_costs_diffs = []

    period_wealths_0cg = []
    capital_gains_0cg = []
    trade_costs_0cg = []
    tracking_errors_0cg = []
    cash_amounts_0cg = []
    portfolio_returns_0cg = []
    trade_costs_diffs_0cg = []

    period_wealths.append(invested_wealth)
    period_wealths_0cg.append(invested_wealth)

    years_left = investment_horizon

    changed_covariance = covariance.copy()
    changed_covariance[-1, -1] = 0.05

    cash = invested_wealth - (transactions.get_quantities().T @ old_prices).item()
    cash_0cg = cash

    cash_amounts.append(cash)
    cash_amounts_0cg.append(cash_0cg)

    transactions_0cg = copy.deepcopy(transactions)

    while years_left > 0:
        simulated_return = simulation_utils.sample_log_normal_from_arithmetic_parameters(
            arithmetic_means=returns,
            arithmetic_covariance=covariance,
            n_periods=1,
            n_samples=1
        ).reshape(-1, 1)

        if years_left != investment_horizon:
            cash += 2000
            cash_0cg += 2000

        simulated_returns.append(simulated_return)

        weights = (transactions.get_quantities() / (np.sum(transactions.get_quantities()) + cash)).reshape(-1, 1)

        new_prices = np.round(old_prices * (1 + simulated_return), 4)

        quantities = transactions.get_quantities().flatten()
        portfolio_returns.append((np.dot(quantities, new_prices) - np.dot(quantities, old_prices)) / (np.dot(quantities, old_prices)))

        (transactions,
         cash,
         portfolio_value,
         capital_gain,
         trade_cost,
         tracking_error,
         trade_costs_diff) = run_tax_optimizer(target_weights,
                                               transactions,
                                               new_prices,
                                               changed_covariance,
                                               cash,
                                               investment_horizon,
                                               years_left)

        cash_amounts.append(cash)
        capital_gains.append(capital_gain)
        trade_costs.append(trade_cost)
        tracking_errors.append(tracking_error)
        trade_costs_diffs.append(trade_costs_diff)
        period_wealths.append(portfolio_value + cash)

        weights_0cg = (transactions_0cg.get_quantities() / (np.sum(transactions_0cg.get_quantities()) + cash_0cg)).reshape(-1, 1)

        quantities_0cg = transactions_0cg.get_quantities().flatten()
        portfolio_returns_0cg.append((np.dot(quantities_0cg, new_prices) - np.dot(quantities_0cg, old_prices)) / (np.dot(quantities_0cg, old_prices)))

        (transactions_0cg,
         cash_0cg,
         portfolio_value_0cg,
         capital_gain_0cg,
         trade_cost_0cg,
         tracking_error_0cg,
         trade_costs_diff_0cg) = run_tax_optimizer_with_elbow(target_weights,
                                                   transactions_0cg,
                                                   new_prices,
                                                   changed_covariance,
                                                   cash_0cg,
                                                   investment_horizon,
                                                   years_left, allowed_capital_gains=0)

        cash_amounts_0cg.append(cash_0cg)
        capital_gains_0cg.append(capital_gain_0cg)
        trade_costs_0cg.append(trade_cost_0cg)
        tracking_errors_0cg.append(tracking_error_0cg)
        trade_costs_diffs_0cg.append(trade_costs_diff_0cg)
        period_wealths_0cg.append(portfolio_value_0cg + cash_0cg)

        years_left -= 1
        old_prices = new_prices

    return simulated_returns, period_wealths, np.round(np.sum(np.maximum(np.array(capital_gains), 0)), 3), sum(
        trade_costs), tracking_errors, portfolio_returns, cash_amounts, trade_costs_diffs, period_wealths_0cg, np.round(np.sum(np.maximum(np.array(capital_gains_0cg), 0)), 3), sum(
        trade_costs_0cg), tracking_errors_0cg, portfolio_returns_0cg, cash_amounts_0cg, trade_costs_diffs_0cg, np.maximum(np.array(capital_gains), 0)

#%%
def simulate_investment_multiple_times(returns: np.ndarray,
                                       covariance: np.ndarray,
                                       target_weights: np.ndarray,
                                       old_prices: np.ndarray,
                                       transactions: Transactions,
                                       invested_wealth: float,
                                       investment_horizon: int,
                                       n_sims: int = 1000):
    simulated_returns_sims = []

    terminal_wealths_sims = []
    capital_gains_sims = []
    trade_costs_sims = []
    tracking_errors_sims = []
    portfolio_returns_sims = []
    all_capital_gains_sims = []

    terminal_wealths_0cg_sims = []
    capital_gains_0cg_sims = []
    trade_costs_0cg_sims = []
    tracking_errors_0cg_sims = []
    portfolio_returns_0cg_sims = []

    for i in range(n_sims):
        print(i)
        (simulated_returns,
         period_wealths,
         capital_gains,
         trade_costs,
         tracking_errors,
         portfolio_returns,
         _,
         _,
         period_wealths_0cg,
         capital_gains_0cg,
         trade_costs_0cg,
         tracking_errors_0cg,
         portfolio_returns_0cg,
         _,
         _,
         all_capital_gains) = simulate_investment(returns=returns,
                                                  covariance=covariance,
                                                  target_weights=target_weights,
                                                  old_prices=old_prices,
                                                  transactions=copy.deepcopy(transactions),
                                                  invested_wealth=invested_wealth,
                                                  investment_horizon=investment_horizon)

        simulated_returns_sims.append(simulated_returns)

        terminal_wealths_sims.append(round(period_wealths[-1], 3))
        capital_gains_sims.append(capital_gains)
        trade_costs_sims.append(trade_costs)
        tracking_errors_sims.append(tracking_errors)
        portfolio_returns_sims.append(portfolio_returns)
        all_capital_gains_sims.append(all_capital_gains)

        terminal_wealths_0cg_sims.append(round(period_wealths_0cg[-1], 3))
        capital_gains_0cg_sims.append(capital_gains_0cg)
        trade_costs_0cg_sims.append(trade_costs_0cg)
        tracking_errors_0cg_sims.append(tracking_errors_0cg)
        portfolio_returns_0cg_sims.append(portfolio_returns_0cg)

    return simulated_returns_sims, terminal_wealths_sims, portfolio_returns_sims, capital_gains_sims, trade_costs_sims, tracking_errors_sims, terminal_wealths_0cg_sims, portfolio_returns_0cg_sims, capital_gains_0cg_sims, trade_costs_0cg_sims, tracking_errors_0cg_sims, all_capital_gains_sims

#%%
returns = np.array(ef_response.annualReturns)
covariance = np.array(ef_response.covarianceMatrix)

simulated_returns_sims, terminal_wealths_sims, portfolio_returns_sims, capital_gains_sims, trade_costs_sims, tracking_errors_sims, terminal_wealths_0cg_sims, portfolio_returns_0cg_sims, capital_gains_0cg_sims, trade_costs_0cg_sims, tracking_errors_0cg_sims, all_capital_gains_sims = simulate_investment_multiple_times(
    returns=returns,
    covariance=covariance,
    target_weights=weights,
    old_prices=old_prices.reshape(-1, 1),
    transactions=copy.deepcopy(transactions),
    invested_wealth=investment_amount,
    investment_horizon=investment_horizon
)
#%%
folder_name = f"{investment_amount}_{investment_horizon}_all_cg"
#%%
simulated_returns_sims = np.array(simulated_returns_sims).squeeze()

terminal_wealths_sims = np.array(terminal_wealths_sims)
terminal_wealths_0cg_sims = np.array(terminal_wealths_0cg_sims)

capital_gains_sims = np.array(capital_gains_sims)
capital_gains_0cg_sims = np.array(capital_gains_0cg_sims)

tracking_errors_sims = np.array(tracking_errors_sims)
tracking_errors_0cg_sims = np.array(tracking_errors_0cg_sims)

trade_costs_sims = np.array(trade_costs_sims)
trade_costs_0cg_sims = np.array(trade_costs_0cg_sims)

portfolio_returns_sims = np.array(portfolio_returns_sims).squeeze()
portfolio_returns_0cg_sims = np.array(portfolio_returns_0cg_sims).squeeze()
#%%
np.save(f"{folder_name}/simulated_returns.npy", simulated_returns_sims)
#%%
np.save(f"{folder_name}/terminal_wealths.npy", terminal_wealths_sims)
np.save(f"{folder_name}/portfolio_returns.npy", portfolio_returns_sims)
np.save(f"{folder_name}/capital_gains.npy", capital_gains_sims)
np.save(f"{folder_name}/trade_costs.npy", trade_costs_sims)
np.save(f"{folder_name}/tracking_errors.npy", tracking_errors_sims)
np.save(f"{folder_name}/all_capital_gains.npy", all_capital_gains_sims)
#%%
np.save(f"{folder_name}/terminal_wealths_0cg.npy", terminal_wealths_0cg_sims)
np.save(f"{folder_name}/portfolio_returns_0cg.npy", portfolio_returns_0cg_sims)
np.save(f"{folder_name}/capital_gains_0cg.npy", capital_gains_0cg_sims)
np.save(f"{folder_name}/trade_costs_0cg.npy", trade_costs_0cg_sims)
np.save(f"{folder_name}/tracking_errors_0cg.npy", tracking_errors_0cg_sims)
#%%
terminal_wealths_sims = np.load(f"{folder_name}/terminal_wealths.npy")
terminal_wealths_0cg_sims = np.load(f"{folder_name}/terminal_wealths_0cg.npy")
#%%
plt.hist(terminal_wealths_sims, density=True, bins=50, color='blue', edgecolor='none', label="Unconstrained")
plt.hist(terminal_wealths_0cg_sims, density=True, bins=50, color='red', alpha=0.7, edgecolor='none', label="Constrained")
plt.legend(loc='upper right')
plt.title(f"Terminal Wealths, {investment_amount} invested, {investment_horizon} years")
plt.xlabel("Terminal Wealth")
plt.show()
#%%
plt.hist(terminal_wealths_sims, density=True, bins=50, color='blue', edgecolor='none', label="Unconstrained")
plt.hist(terminal_wealths_0cg_sims, density=True, bins=50, color='red', alpha=0.7, edgecolor='none', label="Constrained")
plt.legend(loc='upper right')
plt.title(f"Terminal Wealths, {investment_amount} invested, {investment_horizon} years")
plt.xlabel("Terminal Wealth")
plt.show()
#%%
top_20_indices = np.argsort(capital_gains_sims)[-20:][::-1]
bot_20_indices = np.argsort(capital_gains_sims)[:20]

top_20_tw = np.argsort(terminal_wealths_0cg_sims - terminal_wealths_sims)[-20:][::-1]
bot_20_tw = np.argsort(terminal_wealths_0cg_sims - terminal_wealths_sims)[:20]
#%%
np.mean(tracking_errors_sims[top_20_indices])
#%%
np.mean(tracking_errors_sims[bot_20_indices])
#%%
np.mean(portfolio_returns_sims, axis=0)
#%%
simulated_returns_sims[top_20_indices].shape
#%%
plt.plot(np.mean(simulated_returns_sims[top_20_indices, :, :], axis=0))
plt.show()
#%%
plt.plot(np.mean(simulated_returns_sims[np.ix_(top_20_indices, np.arange(5), np.argwhere(weights > 0).ravel())], axis=0))
plt.show()
#%%
simulated_returns_sims[np.ix_(top_20_indices, np.arange(5), np.argwhere(weights > 0).ravel())].shape
#%%
simulated_returns_sims.shape
#%%
plt.plot(np.mean(portfolio_returns_sims[top_20_indices], axis=0).T)
plt.plot(np.mean(portfolio_returns_sims[bot_20_indices], axis=0).T)
plt.axhline(y=np.mean(portfolio_returns_sims, axis=0)[0], color='r', linestyle='--')
#%%
plt.plot(np.mean(portfolio_returns_sims[top_20_tw], axis=0).T)
plt.plot(np.mean(portfolio_returns_sims[bot_20_tw], axis=0).T)
plt.axhline(y=np.mean(portfolio_returns_sims, axis=0)[0], color='r', linestyle='--')
#%%
capital_gains_sims[top_20_indices]
#%%
plt.hist(terminal_wealths_sims[top_20_indices], density=True, bins=50, color='blue', edgecolor='none', label="Unconstrained")
plt.hist(terminal_wealths_0cg_sims[top_20_indices], density=True, bins=50, color='red', alpha=0.7, edgecolor='none', label="Constrained")
plt.legend(loc='upper right')
plt.title(f"Terminal Wealths, {investment_amount} invested, {investment_horizon} years")
plt.xlabel("Terminal Wealth")
plt.show()
#%%

#%%
capital_gains_sims[top_20_tw]
#%%
np.mean(capital_gains_sims)
#%%
np.mean(portfolio_returns_sims)
#%%
np.mean(trade_costs_sims)
#%%
np.mean(terminal_wealths_sims)
#%%
np.var(terminal_wealths_sims)
#%%
np.mean(terminal_wealths_0cg_sims)
#%%
np.var(terminal_wealths_0cg_sims)
#%%
terminal_wealths_100000_5 = np.loadtxt("100000_5/terminal_wealths.txt")
mean_returns_100000_5 = np.loadtxt("100000_5/portfolio_returns.txt")
capital_gains_100000_5 = np.loadtxt("100000_5/capital_gains.txt")
trade_costs_100000_5 = np.loadtxt("100000_5/trade_costs.txt")
tracking_errors_100000_5 = np.loadtxt("100000_5/tracking_errors.txt")

terminal_wealths_0cg_100000_5 = np.loadtxt("100000_5/terminal_wealths_0cg.txt")
mean_returns_0cg_100000_5 = np.loadtxt("100000_5/portfolio_returns_0cg.txt")
capital_gains_0cg_100000_5 = np.loadtxt("100000_5/capital_gains_0cg.txt")
trade_costs_0cg_100000_5 = np.loadtxt("100000_5/trade_costs_0cg.txt")
tracking_errors_0cg_100000_5 = np.loadtxt("100000_5/tracking_errors_0cg.txt")
#%%
print(np.mean(terminal_wealths_100000_5))
print(np.mean(mean_returns_100000_5))
print(np.mean(capital_gains_100000_5))
print(np.mean(trade_costs_100000_5))
print(np.mean(tracking_errors_100000_5))
#%%
print(np.mean(terminal_wealths_0cg_100000_5))
print(np.mean(mean_returns_0cg_100000_5))
print(np.mean(capital_gains_0cg_100000_5))
print(np.mean(trade_costs_0cg_100000_5))
print(np.mean(tracking_errors_0cg_100000_5))
#%%
terminal_wealths_100000_20 = np.loadtxt("100000_20/terminal_wealths.txt")
mean_returns_100000_20 = np.loadtxt("100000_20/portfolio_returns.txt")
capital_gains_100000_20 = np.loadtxt("100000_20/capital_gains.txt")
trade_costs_100000_20 = np.loadtxt("100000_20/trade_costs.txt")
tracking_errors_100000_20 = np.loadtxt("100000_20/tracking_errors.txt")

terminal_wealths_0cg_100000_20 = np.loadtxt("100000_20/terminal_wealths_0cg.txt")
mean_returns_0cg_100000_20 = np.loadtxt("100000_20/portfolio_returns_0cg.txt")
capital_gains_0cg_100000_20 = np.loadtxt("100000_20/capital_gains_0cg.txt")
trade_costs_0cg_100000_20 = np.loadtxt("100000_20/trade_costs_0cg.txt")
tracking_errors_0cg_100000_20 = np.loadtxt("100000_20/tracking_errors_0cg.txt")
#%%
print(np.mean(terminal_wealths_100000_20))
print(np.mean(mean_returns_100000_20))
print(np.mean(capital_gains_100000_20))
print(np.mean(trade_costs_100000_20))
print(np.mean(tracking_errors_100000_20))
#%%
print(np.mean(terminal_wealths_0cg_100000_20))
print(np.mean(mean_returns_0cg_100000_20))
print(np.mean(capital_gains_0cg_100000_20))
print(np.mean(trade_costs_0cg_100000_20))
print(np.mean(tracking_errors_0cg_100000_20))
#%%
terminal_wealths_1000_5 = np.loadtxt("1000_5/terminal_wealths.txt")
mean_returns_1000_5 = np.loadtxt("1000_5/portfolio_returns.txt")
capital_gains_1000_5 = np.loadtxt("1000_5/capital_gains.txt")
trade_costs_1000_5 = np.loadtxt("1000_5/trade_costs.txt")
tracking_errors_1000_5 = np.loadtxt("1000_5/tracking_errors.txt")

terminal_wealths_0cg_1000_5 = np.loadtxt("1000_5/terminal_wealths_0cg.txt")
mean_returns_0cg_1000_5 = np.loadtxt("1000_5/portfolio_returns_0cg.txt")
capital_gains_0cg_1000_5 = np.loadtxt("1000_5/capital_gains_0cg.txt")
trade_costs_0cg_1000_5 = np.loadtxt("1000_5/trade_costs_0cg.txt")
tracking_errors_0cg_1000_5 = np.loadtxt("1000_5/tracking_errors_0cg.txt")
#%%
print(np.mean(terminal_wealths_1000_5))
print(np.mean(mean_returns_1000_5))
print(np.mean(capital_gains_1000_5))
print(np.mean(trade_costs_1000_5))
print(np.mean(tracking_errors_1000_5))
#%%
print(np.mean(terminal_wealths_0cg_1000_5))
print(np.mean(mean_returns_0cg_1000_5))
print(np.mean(capital_gains_0cg_1000_5))
print(np.mean(trade_costs_0cg_1000_5))
print(np.mean(tracking_errors_0cg_1000_5))
#%%
terminal_wealths_1000_20 = np.loadtxt("1000_20/terminal_wealths.txt")
mean_returns_1000_20 = np.loadtxt("1000_20/portfolio_returns.txt")
capital_gains_1000_20 = np.loadtxt("1000_20/capital_gains.txt")
trade_costs_1000_20 = np.loadtxt("1000_20/trade_costs.txt")
tracking_errors_1000_20 = np.loadtxt("1000_20/tracking_errors.txt")

terminal_wealths_0cg_1000_20 = np.loadtxt("1000_20/terminal_wealths_0cg.txt")
mean_returns_0cg_1000_20 = np.loadtxt("1000_20/portfolio_returns_0cg.txt")
capital_gains_0cg_1000_20 = np.loadtxt("1000_20/capital_gains_0cg.txt")
trade_costs_0cg_1000_20 = np.loadtxt("1000_20/trade_costs_0cg.txt")
tracking_errors_0cg_1000_20 = np.loadtxt("1000_20/tracking_errors_0cg.txt")
#%%
print(np.mean(terminal_wealths_1000_20))
print(np.mean(mean_returns_1000_20))
print(np.mean(capital_gains_1000_20))
print(np.mean(trade_costs_1000_20))
print(np.mean(tracking_errors_1000_20))
#%%
print(np.mean(terminal_wealths_0cg_1000_20))
print(np.mean(mean_returns_0cg_1000_20))
print(np.mean(capital_gains_0cg_1000_20))
print(np.mean(trade_costs_0cg_1000_20))
print(np.mean(tracking_errors_0cg_1000_20))
#%%
capm_optimizer = CapmLpOptimizer()
#%%
returns = np.array(ef_response.annualReturns)
covariance = np.array(ef_response.covarianceMatrix)
#%%
(single_period_efficient_frontier) = capm_optimizer.calculate_efficient_frontier(returns=returns, covariance=covariance)
#%%
returns
#%%
single_period_efficient_frontier
#%%
w_sharpe = np.array(single_period_efficient_frontier[55, :])
w_max_risk = np.array(single_period_efficient_frontier[-1, :])
#%%
w_80 = np.array(single_period_efficient_frontier[80, :])
#%%
w_sharpe
#%%
w_max_risk
#%%
investment_amount = 100000
investment_horizon = 20
#%%
t_wealths_sharpe = []
t_wealths_max_risk = []
t_wealths_80 = []
for _ in range(1000):
    wealth_sharpe = investment_amount
    wealth_max_risk = investment_amount
    wealth_80 = investment_amount
    for _ in range(investment_horizon):
        simulated_return = simulation_utils.sample_log_normal_from_arithmetic_parameters(
                    arithmetic_means=returns,
                    arithmetic_covariance=covariance,
                    n_periods=1,
                    n_samples=1
                ).reshape(-1, 1)
        wealth_sharpe = wealth_sharpe * (w_sharpe @ simulated_return + 1)[0]
        wealth_max_risk = wealth_max_risk * (w_max_risk @ simulated_return + 1)[0]
        wealth_80 = wealth_80 * (w_80 @ simulated_return + 1)[0]
    t_wealths_sharpe.append(wealth_sharpe)
    t_wealths_max_risk.append(wealth_max_risk)
    t_wealths_80.append(wealth_80)
#%%
plt.hist(t_wealths_sharpe, density=True, bins=50, color='blue', edgecolor='none', label="Max Sharpe")
plt.hist(t_wealths_max_risk, density=True, bins=50, color='red', alpha=0.7, edgecolor='none', label="Max Risk")
# plt.hist(t_wealths_80, density=True, bins=50, color='green', alpha=0.7, edgecolor='none', label="80th portfolio")
plt.legend(loc='upper right')
plt.title(f"Terminal Wealths, {investment_amount} invested, {investment_horizon} years")
plt.xlabel("Terminal Wealth")
plt.show()
#%%
plt.hist(t_wealths_sharpe, density=True, bins=50, color='blue', edgecolor='none', label="Max Sharpe")
plt.hist(t_wealths_max_risk, density=True, bins=50, color='red', alpha=0.7, edgecolor='none', label="Max Risk")
plt.legend(loc='upper right')
plt.title(f"Terminal Wealths, {investment_amount} invested, {investment_horizon} years")
plt.xlabel("Terminal Wealth")
plt.show()