#%%
from utils.pydantic_models import Product
%load_ext autoreload
%autoreload 2
#%%
import matplotlib.pyplot as plt

import os
from pathlib import Path
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from scipy import stats

from configuration import config
from utils import analytics_utils
from utils import util
from requests_.requests import EfficientFrontierRequest, BacktestRequest
from responses_.responses import EfficientFrontierResponse
from main import calculate_efficient_frontier, calculate_portfolio_values
#%%
import warnings
warnings.filterwarnings("ignore")

notebook_dir = os.path.dirname(os.path.abspath("__file__"))
parent_dir = Path(notebook_dir).parent
os.chdir(parent_dir)
print(f"Current working directory: {os.getcwd()}")

settings = config.get_settings()

#%%
investment_amount = 10000
investment_horizon = 1
#%%
ef_request = EfficientFrontierRequest(investmentAmount=investment_amount, investmentHorizon=investment_horizon)

ef_response: EfficientFrontierResponse = calculate_efficient_frontier(ef_request)
#%%
low_risk_products = ef_response.frontier[0].products
low_medium_risk_products = ef_response.frontier[25].products
medium_risk_products = ef_response.frontier[50].products
high_medium_risk_products = ef_response.frontier[75].products
high_risk_products = ef_response.frontier[99].products

products_list = [low_risk_products, low_medium_risk_products, medium_risk_products, high_medium_risk_products, high_risk_products]

low_risk_isins = [product.isin for product in low_risk_products]
low_medium_risk_isins = [product.isin for product in low_medium_risk_products]
medium_risk_isins = [product.isin for product in medium_risk_products]
high_medium_risk_isins = [product.isin for product in high_medium_risk_products]
high_risk_isins = [product.isin for product in high_risk_products]

isins_list = [low_risk_isins, low_medium_risk_isins, medium_risk_isins, high_medium_risk_isins, high_risk_isins]
#%%
for products, isins in zip(products_list, isins_list):
    bt_request = BacktestRequest(investmentAmount=investment_amount, endingDatetime=datetime.today(), portfolio=products)
    bt_response = calculate_portfolio_values(bt_request)
    values = [point.portfolio_value for point in bt_response.points]
    values.insert(0, investment_amount)
    returns = pd.Series(values).pct_change().dropna().to_list()
    returns = np.array(returns)
    mean_return, covariance = util.get_annual_arithmetic_mean_and_covariance(pd.DataFrame(returns))
    print(f"Mean annual return: {mean_return[0][0]*100:.2f}%")
    print(f"Std: {np.sqrt(covariance[0][0])*100:.2f}%")

        
    confidence_level = 0.95
    var_percentile = 100 * (1 - confidence_level)
    VaR = np.percentile(returns, var_percentile) 
    
    print(f"Value at Risk (VaR) at {confidence_level * 100}% confidence level: {VaR *100:.2f}%")
    
    losses_beyond_VaR = returns[returns <= (VaR)]
    CVaR = losses_beyond_VaR.mean()
    print(f"Conditional Value at Risk (CVaR): {CVaR * 100:.2f}%")
    
    CAGR = (values[-1] / values[0])**(1 / settings.time_horizon) - 1
    print(f"Compound Annual Growth Rate (CAGR): {CAGR * 100:.2f}%")
    
    values = pd.Series(values)
    running_max = values.cummax()
    drawdown = (values - running_max) / running_max
    max_drawdown = drawdown.min()
    print(f"Max Drawdown: {max_drawdown*100:.2f}%\n")

#%%
for index in [0, 25, 50, 75, 99]:
    print(f"Mean return: {ef_response.frontier[index].annualReturn * 100:.2f}%")
    print(f"Std: {np.sqrt(ef_response.frontier[index].variance) * 100:.2f}%")
    
    log_mean_return, log_variance = util.get_log_mean_and_variance_from_arithmetic(ef_response.frontier[index].annualReturn, ef_response.frontier[index].variance)
    
    VaR = - (1 - np.exp(log_mean_return - 1.65 * np.sqrt(log_variance)))
    print(f"Value at Risk (VaR) at {confidence_level * 100}% confidence level: {VaR * 100:.2f}%")
    
    CVaR = - (1 - np.exp(log_mean_return + log_variance/2)*stats.norm.cdf(stats.norm.ppf(0.05) - np.sqrt(log_variance))/0.05)
    print(f"Conditional Value at Risk (CVaR): {CVaR * 100:.2f}%")

    
    expected_value = investment_amount * np.exp(log_mean_return * investment_horizon)
    
    CAGR = expected_value / investment_amount - 1
    print(f"Compound Annual Growth Rate (CAGR): {CAGR*100:.2f}%\n")
    
#%%
 # plt.figure(figsize=(10, 6))
    # 
    # # Define bins for the histogram
    # bins = np.linspace(min(an_returns), max(an_returns), 50)
    # 
    # # Plot histogram of returns
    # counts, bin_edges, _ = plt.hist(an_returns, bins=bins, color='blue', alpha=0.6, label='Returns Distribution')
    # 
    # # Plot VaR line
    # plt.axvline(VaR * np.sqrt(252), color='red', linestyle='--', linewidth=2, label=f'VaR')
    # 
    # # Plot CVaR as a vertical line or point
    # plt.axvline(CVaR * np.sqrt(252), color='orange', linestyle='--', linewidth=2, label=f'CVaR')
    # 
    # # Add labels and title
    # plt.title(f"VaR and CVaR Visualization for Portfolio")
    # plt.xlabel("Returns")
    # plt.ylabel("Frequency")
    # plt.legend()
    # 
    # plt.show()
    
    
    # plt.figure(figsize=(10, 6))
    # 
    # # Define bins for the histogram
    # bins = np.linspace(min(returns), max(returns), 50)
    # 
    # # Plot histogram of returns
    # counts, bin_edges, _ = plt.hist(returns, bins=bins, color='blue', alpha=0.6, label='Returns Distribution')
    # 
    # # Plot VaR line
    # plt.axvline(VaR/np.sqrt(252), color='red', linestyle='--', linewidth=2, label=f'VaR')
    # 
    # # Plot CVaR as a vertical line or point
    # plt.axvline(CVaR/np.sqrt(252), color='orange', linestyle='--', linewidth=2, label=f'CVaR')
    # 
    # # Add labels and title
    # plt.title(f"VaR and CVaR Visualization for Portfolio")
    # plt.xlabel("Returns")
    # plt.ylabel("Frequency")
    # plt.legend()
    # 
    # plt.show()
#%%
import pandas as pd
import numpy as np


prices = pd.DataFrame([[100, 200, 50], [101, 204, 50.5], [102.01, 208.08, 51.005], [103.0301, 212.2416, 51.51505]])
cov = np.cov(prices.pct_change().dropna(), rowvar=False)
cov