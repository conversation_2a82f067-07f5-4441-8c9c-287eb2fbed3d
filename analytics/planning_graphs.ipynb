#%%
from analytics.tax_test2 import investment_horizon
%load_ext autoreload
%autoreload 2
#%%
import os
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

from configuration import config
from requests_.requests import EfficientFrontierRequest, BacktestRequest
from responses_.responses import EfficientFrontierResponse
from main import calculate_efficient_frontier
from optimizers.efficient_frontier_optimizer import CapmLpOptimizer
from repositories.old_mongo_repositories import metadata_repository
from typing import List, Tuple
#%%
import warnings

warnings.filterwarnings("ignore")

notebook_dir = os.path.dirname(os.path.abspath("__file__"))
parent_dir = Path(notebook_dir).parent
os.chdir(parent_dir)
print(f"Current working directory: {os.getcwd()}")

settings = config.get_settings()
#%%
capm_optimizer = CapmLpOptimizer()
#%%
ef_request = EfficientFrontierRequest(investmentAmount=10000, investmentHorizon=20)

ef_response: EfficientFrontierResponse = calculate_efficient_frontier(ef_request)
#%%
product_covariance = np.array(ef_response.covarianceMatrix)
product_expected_returns = np.array(ef_response.annualReturns)
#%%
(sp_efficient_frontier) = capm_optimizer.calculate_efficient_frontier(returns=product_expected_returns, covariance=product_covariance)
#%%
sp_efficient_frontier.shape
#%%
from utils import simulation_utils

def simulate_sp_ef_returns(product_expected_returns: np.ndarray, product_covariance: np.ndarray, sp_efficient_frontier: np.ndarray) -> np.ndarray:
        product_returns = simulation_utils.sample_log_normal_from_arithmetic_parameters(product_expected_returns, product_covariance, n_samples=1000, n_periods=1)
        sp_efficient_frontier_returns = product_returns @ sp_efficient_frontier.T
        return sp_efficient_frontier_returns
#%%
sp_ef_returns = simulate_sp_ef_returns(product_expected_returns, product_covariance, sp_efficient_frontier)
#%%
from abc import ABC, abstractmethod
import numpy as np
from enum import Enum

class ReturnCalculationEnum(str, Enum):
    TWR = "time_weighted_return"
    MWR = "money_weighted_return"


class ReturnCalculator(ABC):

    @abstractmethod
    def calculate_strategy_returns(self, simulated_returns: np.ndarray, historical_returns: np.ndarray = None, **kwargs) -> np.ndarray:
        pass

    @abstractmethod
    def calculate_cumulative_returns(self, simulated_returns: np.ndarray, **kwargs) -> np.ndarray:
        pass

    @abstractmethod
    def calculate_cumulative_return(self, simulated_returns: np.ndarray, **kwargs) -> float:
        pass

    @staticmethod
    def _slice_returns(simulated_returns: np.ndarray, historical_returns: np.ndarray) -> np.ndarray:
        n_sims = simulated_returns.shape[0]
        n_periods = simulated_returns.shape[1]

        starting_indices = np.arange(n_periods)
        ending_indices = np.arange(n_sims - n_periods + 1, n_sims + 1)

        sliced_returns = np.vstack(
            [simulated_returns[start:end, i] for i, (start, end) in enumerate(zip(starting_indices, ending_indices))]
        ).T
        #
        # if historical_returns is not None:
        #     sliced_returns[:, :historical_returns.shape[0]] = historical_returns

        return sliced_returns

    @staticmethod
    def _calculate_cumulative_returns(simulated_returns: np.ndarray) -> np.ndarray:
        if simulated_returns.ndim == 1:
            simulated_returns = simulated_returns.reshape(1, -1)

        log_returns = np.log1p(simulated_returns[:, ::-1])
        cumulative_log_returns = np.cumsum(log_returns, axis=1)[:, ::-1]
        cumulative_returns = np.expm1(cumulative_log_returns)

        return cumulative_returns


class TimeWeightedReturnCalculator(ReturnCalculator):
    def calculate_strategy_returns(self, simulated_returns: np.ndarray, historical_returns: np.ndarray = None, **kwargs) -> np.ndarray:
        sliced_returns = self._slice_returns(simulated_returns, historical_returns)

        cumulative_returns = self.calculate_cumulative_return(sliced_returns, **kwargs)

        return cumulative_returns[:, 0].reshape(-1, 1)

    def calculate_cumulative_returns(self, simulated_returns: np.ndarray, **kwargs) -> np.ndarray:
        return self._calculate_cumulative_returns(simulated_returns)

    def calculate_cumulative_return(self, simulated_returns: np.ndarray, **kwargs) -> float:
        return self.calculate_cumulative_returns(simulated_returns, **kwargs)[0].item()


class MoneyWeightedReturnCalculator(ReturnCalculator):
    @staticmethod
    def _get_alphas(**kwargs) -> np.ndarray:
        alphas = kwargs.get('alphas')
        if alphas is None:
            raise TypeError('Missing required argument alphas')
        return alphas.reshape(-1, 1)

    def calculate_strategy_returns(self, simulated_returns: np.ndarray, historical_returns: np.ndarray = None, **kwargs) -> np.ndarray:
        sliced_returns = self._slice_returns(simulated_returns, historical_returns)
        cumulative_returns = self.calculate_cumulative_returns(sliced_returns, **kwargs)

        return cumulative_returns

    def calculate_cumulative_returns(self, simulated_returns: np.ndarray, **kwargs) -> np.ndarray:
        alphas = self._get_alphas(**kwargs)
        print(simulated_returns[0])
        cumulative_returns = self._calculate_cumulative_returns(simulated_returns)
        print(cumulative_returns[0])
        return cumulative_returns @ alphas

    def calculate_cumulative_return(self, simulated_returns: np.ndarray, **kwargs) -> float:
        simulated_returns = simulated_returns.reshape(1, -1)
        return self.calculate_cumulative_returns(simulated_returns, **kwargs).item()


class ReturnCalculatorFactory:
    @staticmethod
    def create_return_calculator(return_calculator_type: ReturnCalculationEnum) -> ReturnCalculator:
        if return_calculator_type == ReturnCalculationEnum.TWR:
            return TimeWeightedReturnCalculator()
        elif return_calculator_type == ReturnCalculationEnum.MWR:
            return MoneyWeightedReturnCalculator()
        else:
            raise ValueError(f'Unsupported return calculator type: {return_calculator_type}')

#%%
weights = np.array([[0.1, 0.2, 0.3, 0.4], [0.2, 0.3, 0.4, 0.1], [0.3, 0.4, 0.1, 0.2], [0.4, 0.1, 0.2, 0.3], [0.5, 0.5, 0.0, 0.0]])
#%%
isins = ['IE00BYXYYL56', 'LU0290355717', 'DE000A0S9GB0', 'LU1437018838']
#%%
efficient_frontier = pd.DataFrame(weights, columns=isins)
#%%
efficient_frontier
#%%
portfolio_indices = np.array([0, 1, 2, 3, 4])
#%%
metadata_repository = metadata_repository.MetadataRepository()
#%%
metadata_repository.get_fields_for_ISINs(isins, ['assetClass'])
#%%
isins = efficient_frontier.columns.values.tolist()
asset_classes = metadata_repository.get_fields_for_ISINs(efficient_frontier.columns.values.tolist(), ['assetClass'])['assetClass']
isin_to_asset_class = dict(zip(isins, asset_classes))
#%%
isin_to_asset_class
#%% md
stacked bar:
    - receives portfolio index for each period
    - returns asset class weights for each period, weights of each asset?
#%%
def weights_over_time(portfolio_indices: np.ndarray, efficient_frontier: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    asset_class_weights_over_time = []
    product_weights_over_time = []

    isins = efficient_frontier.columns.values.tolist()
    asset_classes = metadata_repository.get_fields_for_ISINs(isins, ['assetClass'])['assetClass']
    isin_to_asset_class = dict(zip(isins, asset_classes))

    for frontier_index in portfolio_indices:
        product_weights = efficient_frontier.iloc[frontier_index]
        asset_class_series = product_weights.rename(index=isin_to_asset_class)
        asset_class_weights = asset_class_series.groupby(level=0).sum()

        asset_class_weights_over_time.append(asset_class_weights.to_dict())
        product_weights_over_time.append(product_weights.to_dict())

    asset_class_df = pd.DataFrame(asset_class_weights_over_time).fillna(0)
    product_weights_df = pd.DataFrame(product_weights_over_time).fillna(0)

    return asset_class_df, product_weights_df
#%%
selected_weights = efficient_frontier.iloc[portfolio_indices]
#%%
selected_weights
#%%
from typing import Dict
import pandas as pd
import numpy as np

def weights_over_time(portfolio_indices: np.ndarray, efficient_frontier: pd.DataFrame) -> Dict[str, Dict[str, List[float]]]:
    isins = efficient_frontier.columns.tolist()
    asset_classes = metadata_repository.get_fields_for_ISINs(isins, ['assetClass'])['assetClass']
    isin_to_asset_class = dict(zip(isins, asset_classes))

    investment_horizon = len(portfolio_indices)

    strategy: Dict[str, Dict[str, List[float]]] = {}

    selected_weights = efficient_frontier.iloc[portfolio_indices]

    for isin in isins:
        asset_class = isin_to_asset_class[isin]
        weights = selected_weights[isin].tolist()

        if asset_class not in strategy:
            strategy[asset_class] = {}

        strategy[asset_class][isin] = weights

    for asset_class_values in strategy.values():
        sums = [0.0] * investment_horizon

        for isin_weights in asset_class_values.values():
            for i in range(investment_horizon):
                sums[i] += isin_weights[i]

        asset_class_values['sum'] = sums

    return strategy
#%%
weights_over_time(portfolio_indices, efficient_frontier)
#%%
asset_class_df, product_weights_df = weights_over_time(portfolio_indices, efficient_frontier)
#%%
asset_class_df
#%%
product_weights_df
#%%
cash_flows = np.array([1000, 200, 200, 200, 200])
cash_flows.cumsum()
#%%
n_paths = 1000
n_periods = 5
n_assets = 4

simulated_returns = np.random.normal(loc=0.01, scale=0.05, size=(n_paths, n_assets))
#%%
simulated_returns_df = pd.DataFrame(simulated_returns, columns=isins)
#%%
simulated_returns_df.mean(axis=0)
#%%
pd.Series(np.percentile(simulated_returns_df, 5, axis=0), index=isins)
#%%
np.percentile(simulated_returns_df, 95, axis=0)
#%%
cash_flows = np.array([1000, 200, 200, 200, 200, 200])
#%%
alphas = cash_flows / cash_flows.sum()
#%%
portfolio_indices = np.array([0, 1, 2, 3, 4, 5])
#%%
alphas
#%%
portfolio_indices
#%%
import numpy as np

def get_portfolio_values(returns: np.ndarray, strategies: np.ndarray, cash_flows: np.ndarray):
    """
    Parameters:
        returns: np.ndarray of shape (n_sims, n_strategies)
                 => total return for each strategy per simulation
        strategies: np.ndarray of shape (n_periods,) with strategy index per period
        cash_flows: np.ndarray of shape (n_periods,) with amount invested at each period

    Returns:
        portfolio_values: np.ndarray of shape (n_sims, n_periods)
                          => value of portfolio at each period
    """
    n_sims, n_strategies = returns.shape
    n_periods = len(strategies)

    portfolio_values = np.zeros((n_sims, n_periods))

    for t in range(n_periods):
        strat_idx = strategies[t]
        growth_factor = 1 + returns[:, strat_idx]  # shape (n_sims,)

        # For each future period, add this cash flow growing to that point
        for future_t in range(t, n_periods):
            # investment grows over (future_t - t) periods
            compound = growth_factor ** (future_t - t)
            portfolio_values[:, future_t] += cash_flows[t] * compound

    return portfolio_values
#%%
get_portfolio_values(sp_ef_returns, portfolio_indices, cash_flows)
#%%
class Strategy:
    def __init__(self, optimistic_portfolio_values: np.ndarray, expected_portfolio_values: np.ndarray, pessimistic_portfolio_values: np.ndarray, invested_amount_over_time: np.ndarray):
        self.optimistic_portfolio_values = optimistic_portfolio_values
        self.expected_portfolio_values = expected_portfolio_values
        self.pessimistic_portfolio_values = pessimistic_portfolio_values
        self.invested_amount_over_time = invested_amount_over_time
#%%
def simulate_sp_ef_returns(product_expected_returns: np.ndarray, covariance: np.ndarray) -> np.ndarray:
        return simulation_utils.sample_log_normal_from_arithmetic_parameters(product_expected_returns, covariance, n_samples=1000, n_periods=1)
#%%
cash_flows.sum()
#%%
def get_cumulative_returns(returns: np.ndarray, portfolio_indices: np.ndarray, alphas: np.ndarray):
    """
    Parameters:
        returns: np.ndarray of shape (n_sims, n_strategies)
        portfolio_indices: np.ndarray of shape (n_periods,), values in 0..n_portfolios-1
        alphas: np.ndarray of shape (n_periods,), e.g. cash_flows / cash_flows.sum()

    Returns:
        cumulative_returns: np.ndarray of shape (n_sims, n_periods)
    """
    n_sims, n_strategies = returns.shape
    n_periods = len(portfolio_indices)

    # Get the return for each period and sim based on the selected strategy
    period_returns = np.zeros((n_sims, n_periods))

    for t in range(n_periods):
        portfolio_idx = portfolio_indices[t]
        alpha = alphas[t]
        period_returns[:, t] = returns[:, portfolio_idx] * alpha

    # Compute cumulative returns (multiplicative model)
    cumulative_returns = np.cumprod(1 + period_returns, axis=1)
    return cumulative_returns
#%%
get_cumulative_returns(sp_ef_returns, portfolio_indices, alphas)
#%%
simulate_sp_ef_returns(product_expected_returns, product_covariance)
#%%
sp_ef_returns
#%%
sp_ef_returns
#%%
return_calculator = ReturnCalculatorFactory.create_return_calculator(ReturnCalculationEnum.MWR)
#%%
sp_ef_returns[:, 1].shape
#%%
alphas
#%%
return_calculator.calculate_cumulative_returns(sp_ef_returns[:, portfolio_indices], alphas=alphas)
#%%
return_calculator.calculate_cumulative_returns(sp_ef_returns[0, portfolio_indices], alphas=alphas)
#%%
def projected_portfolio_value_over_time(portfolio_indices: np.ndarray, efficient_frontier: pd.DataFrame, cash_flows: np.ndarray, simulated_returns: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    expected_portfolio_values, optimistic_portfolio_values, pessimistic_portfolio_values = [[cash_flows[0]] for _ in range(3)]

    invested_amount_over_time = cash_flows.cumsum()

    expected_returns = simulated_returns.mean(axis=0)
    optimistic_returns = np.percentile(simulated_returns, 95, axis=0)
    pessimistic_returns = np.percentile(simulated_returns, 5, axis=0)

    for idx, frontier_index in enumerate(portfolio_indices):
        weights = efficient_frontier.iloc[frontier_index]

        expected_portfolio_return = (weights.values * expected_returns).sum()
        expected_portfolio_values.append((1 + expected_portfolio_return) * expected_portfolio_values[-1] + cash_flows[idx + 1])

        optimistic_portfolio_return = (weights.values * optimistic_returns).sum()
        optimistic_portfolio_values.append((1 + optimistic_portfolio_return) * optimistic_portfolio_values[-1] + cash_flows[idx + 1])

        pessimistic_portfolio_return = (weights.values * pessimistic_returns).sum()
        pessimistic_portfolio_values.append((1 + pessimistic_portfolio_return) * pessimistic_portfolio_values[-1] + cash_flows[idx + 1])

    return np.array(expected_portfolio_values), np.array(optimistic_portfolio_values), np.array(pessimistic_portfolio_values), invested_amount_over_time
#%%
class Strategy:
    def __init__(self, optimistic_portfolio_values: np.ndarray, expected_portfolio_values: np.ndarray, pessimistic_portfolio_values: np.ndarray, invested_amount_over_time: np.ndarray):
        self.optimistic_portfolio_values = optimistic_portfolio_values
        self.expected_portfolio_values = expected_portfolio_values
        self.pessimistic_portfolio_values = pessimistic_portfolio_values
        self.invested_amount_over_time = invested_amount_over_time
#%%
cash_flows = np.array([1000, 200, 200, 200, 200, 200])
#%%
class Strategy:
    def __init__(self, optimistic_portfolio_values: np.ndarray, expected_portfolio_values: np.ndarray, pessimistic_portfolio_values: np.ndarray, invested_amount_over_time: np.ndarray):
        self.optimistic_portfolio_values = optimistic_portfolio_values
        self.expected_portfolio_values = expected_portfolio_values
        self.pessimistic_portfolio_values = pessimistic_portfolio_values
        self.invested_amount_over_time = invested_amount_over_time

#pravim se da san dobila sp_ef_simulated_returns = self.simulate_sp_ef_returns(product_expected_returns, covariance)
def projected_portfolio_value_over_time(portfolio_indices: np.ndarray, sp_ef_simulated_returns: np.ndarray, cash_flows: np.ndarray) -> Strategy:
    cash_flows = np.append(cash_flows, 0)
    invested_amount_over_time = cash_flows.cumsum()

    portfolio_values_sims = np.zeros((sp_ef_simulated_returns.shape[0], portfolio_indices.shape[0] + 1))
    portfolio_values_sims[:, 0] = cash_flows[0]

    for t, frontier_index in enumerate(portfolio_indices):
        returns = sp_ef_simulated_returns[:, frontier_index]
        previous_portfolio_values = portfolio_values_sims[:, t]
        portfolio_values_sims[:, t + 1] = previous_portfolio_values * (1 + returns) + cash_flows[t + 1]

    expected_portfolio_values = portfolio_values_sims.mean(axis=0)
    optimistic_portfolio_values = np.percentile(portfolio_values_sims, 95, axis=0)
    pessimistic_portfolio_values = np.percentile(portfolio_values_sims, 5, axis=0)

    return Strategy(optimistic_portfolio_values=optimistic_portfolio_values, expected_portfolio_values=expected_portfolio_values, pessimistic_portfolio_values=pessimistic_portfolio_values, invested_amount_over_time=invested_amount_over_time)
#%%
portfolio_indices.shape[0]
#%%
sp_ef_returns
#%%
strategy = projected_portfolio_value_over_time(portfolio_indices, sp_ef_returns, cash_flows)
#%%
strategy.expected_portfolio_values
#%%
strategy.invested_amount_over_time
#%%
strategy.optimistic_portfolio_values
#%%
strategy.pessimistic_portfolio_values
#%%
portfolio_indices
#%%
porfolio_values_sims = np.array([[cash_flows[0]] for _ in range(sp_ef_returns.shape[0])])
#%%
porfolio_values_sims
#%%
cash_flows = np.array([1000, 200, 200, 200, 200, 200, 0])
#%%
portfolio_values_sims = np.array([[cash_flows[0]] for _ in range(sp_ef_returns.shape[0])])
for idx, frontier_index in enumerate(portfolio_indices):
    new_portfolio_values = portfolio_values_sims[:, -1:] * (1 + sp_ef_returns[:, frontier_index])[:, np.newaxis] + cash_flows[idx + 1]
    portfolio_values_sims = np.hstack((portfolio_values_sims, new_portfolio_values))
#%%
portfolio_values_sims = np.array([[cash_flows[0]] for _ in range(sp_ef_returns.shape[0])])
#%%
portfolio_values_sims
#%%
import numpy as np

# Original array
a = np.random.rand(1000, 1)

# New values to append (must be shape (1000, 1))
b = np.random.rand(1000, 1)

# Append as second column
result = np.hstack((a, b))

# Check shape
print(result.shape)  # (1000, 2)
#%%
projected_portfolio_value_over_time(portfolio_indices, efficient_frontier, cash_flows, simulated_returns_df)
#%%
def plot_projected_portfolio_values(portfolio_indices: np.ndarray, efficient_frontier: pd.DataFrame, cash_flows: np.ndarray, simulated_returns: pd.DataFrame):
    expected_portfolio_values, optimistic_portfolio_values, pessimistic_portfolio_values, invested_amount_over_time = projected_portfolio_value_over_time(portfolio_indices, efficient_frontier, cash_flows, simulated_returns)

    plt.plot(expected_portfolio_values, label='Expected')
    plt.plot(optimistic_portfolio_values, label='Optimistic')
    plt.plot(pessimistic_portfolio_values, label='Pessimistic')
    plt.plot(invested_amount_over_time, label='Invested')
    plt.legend()
    plt.show()
#%%
plot_projected_portfolio_values(portfolio_indices, efficient_frontier, cash_flows, simulated_returns_df)
#%%
