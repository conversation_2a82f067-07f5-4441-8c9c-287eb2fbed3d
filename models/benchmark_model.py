from typing import Optional

import os
from abc import ABC, abstractmethod

import pandas as pd
import numpy as np
from datetime import datetime

from sklearn.linear_model import LinearRegression

from repositories.old_mongo_repositories.metadata_repository import MetadataRepository
from utils import util
from configuration import config as config
from repositories.old_mongo_repositories import asset_class_repository, etf_indices_repository, etf_repository, risk_free_rate_repository


class PricingModel(ABC):

    def __init__(self):
        self.settings = config.get_settings()
        self.etf_indices_repository = etf_indices_repository.EtfIndicesRepository()
        self.etf_repository = etf_repository.EtfRepository()
        self.metadata_repository = MetadataRepository()
        self.risk_free_rate_repository = risk_free_rate_repository.RiskFreeRateRepository()
        self.asset_class_repository = asset_class_repository.AssetClassesRepository()

    @abstractmethod
    def calculate_products_returns_and_covariance(self, avg_risk_free_rate: float, current_risk_free_rate: float, last_date: datetime, save_analytics: bool = False) -> (np.ndarray, np.ndarray):
        pass


class EquilibriumPricingModel(PricingModel):

    def __init__(self):
        super(EquilibriumPricingModel, self).__init__()

    def calculate_products_returns_and_covariance(self, avg_risk_free_rate: float, current_risk_free_rate: float, last_date: Optional[datetime] = datetime.today(), save_analytics: bool = False) -> (np.ndarray, np.ndarray):
        risk_free_rates, last_document_date = self.risk_free_rate_repository.get_risk_free_rates_in_time_horizon(self.settings.time_horizon, last_date)
        etf_returns = self.etf_repository.get_returns_in_time_horizon(self.settings.time_horizon, last_document_date)

        risk_free_rates, etf_returns = util.synchronize_and_fill_dates([risk_free_rates, etf_returns])
        etf_index_market_caps = self.etf_indices_repository.get_market_caps(last_date)

        asset_class_market_caps = self.asset_class_repository.get_market_caps(last_date)

        asset_class_isins = self.metadata_repository.get_asset_class_ISINs()

        etf_index_weights = self.__calculate_equilibrium_market_portfolio(etf_index_market_caps, asset_class_market_caps, asset_class_isins)

        risk_free_rates_daily = util.get_daily_return(risk_free_rates)

        etf_mean_daily = (etf_returns.mean()).to_numpy()

        etf_excess_cov_daily = ((pd.DataFrame(etf_returns.to_numpy() - risk_free_rates_daily.to_numpy())).cov()).to_numpy()

        etf_mean, etf_cov = util.get_annual_arithmetic_mean_and_covariance(etf_returns)

        avg_risk_free_rate_daily = risk_free_rates_daily.mean().item()

        lambda_ = self.__calculate_implied_risk_aversion_coefficient(avg_risk_free_rate_daily, etf_index_weights, etf_mean_daily, etf_excess_cov_daily)

        expected_excess_returns = self.__calculate_expected_etf_returns(lambda_, etf_index_weights, etf_excess_cov_daily, etf_returns.columns)

        etf_cov = np.pad(etf_cov, pad_width=((0, 1), (0, 1)), mode='constant', constant_values=0)

        columns = self.metadata_repository.get_ISINs()

        expected_returns = np.vstack((util.get_annual_return(expected_excess_returns.values.reshape(-1, 1)) + current_risk_free_rate, current_risk_free_rate))

        etf_returns_with_ter = util.include_ter(expected_returns)

        if save_analytics:
            self.__save_model_analytics(util.get_last_year_and_month(last_date) + '/equilibrium_pricing', lambda_, expected_excess_returns, expected_returns, etf_returns_with_ter, etf_cov, etf_mean,
                                        etf_index_weights, etf_returns, columns)

        return etf_returns_with_ter, etf_cov


    @staticmethod
    def __calculate_weights(market_caps: pd.DataFrame) -> dict:
        total_sum = market_caps.sum(axis=1)
        weights = market_caps.apply(lambda x: x / total_sum)
        return weights.to_dict(orient='records')[0]

    @staticmethod
    def __calculate_equilibrium_market_portfolio(etf_indices_market_caps: pd.DataFrame, asset_class_market_caps: pd.DataFrame, asset_class_isins: dict) -> np.ndarray:
        asset_class_weights = EquilibriumPricingModel.__calculate_weights(asset_class_market_caps)
        etf_indices_weights = {col: 0.0 for col in etf_indices_market_caps.columns}
        for key, value in asset_class_isins.items():
            weights_in_asset_class = EquilibriumPricingModel.__calculate_weights(etf_indices_market_caps[value])
            for k, v in weights_in_asset_class.items():
                etf_indices_weights[k] = v * asset_class_weights[key]

        return np.array(list(etf_indices_weights.values())).reshape(-1, 1)

    @staticmethod
    def __calculate_implied_risk_aversion_coefficient(risk_free_rate, weights: np.ndarray, mean: np.ndarray, cov: np.ndarray) -> float:
        lambda_coefficient = (weights.T @ mean - risk_free_rate) / (weights.T @ cov @ weights)
        return lambda_coefficient.item()

    @staticmethod
    def __calculate_expected_etf_returns(lambda_: float, weights: np.ndarray, cov: np.ndarray, columns: list[str]) -> pd.DataFrame:
        expected_return = lambda_ * cov @ weights
        return pd.DataFrame(expected_return.reshape(1, -1), columns=columns, index=['Return'])

    def __save_model_analytics(self, prefix: str, lambda_: float, expected_excess_returns: pd.DataFrame, expected_returns: np.ndarray, expected_returns_with_ter: np.ndarray, etf_cov: np.ndarray,
                               etf_mean: np.ndarray, etf_index_weights: np.ndarray, etf_returns: pd.DataFrame, column_names: list[str]):
        directory_path = f'{self.settings.analytics_path}{prefix}/'
        os.makedirs(os.path.dirname(directory_path), exist_ok=True)

        lambda_df = pd.DataFrame([lambda_], columns=['lambda'])

        etf_cov_df = pd.DataFrame(etf_cov, columns=column_names)

        etf_mean_df = pd.DataFrame(etf_mean.reshape(1, -1), columns=expected_excess_returns.columns)

        expected_returns = pd.DataFrame(expected_returns.reshape(1, -1), columns=column_names)

        expected_returns_with_ter_df = pd.DataFrame(expected_returns_with_ter.reshape(1, -1), columns=column_names)

        etf_index_weights_df = pd.DataFrame(etf_index_weights.reshape(1, -1), columns=expected_excess_returns.columns)

        lambda_df.to_csv(self.settings.analytics_path + f'{prefix}/lambda.csv', index=False)
        etf_cov_df.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_covariance.csv', index=False)
        expected_excess_returns.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_expected_excess_returns.csv', index=False
        )
        expected_returns.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_expected_returns.csv', index=False)
        expected_returns_with_ter_df.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_expected_returns_with_ter.csv', index=False)
        etf_mean_df.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_mean_returns.csv', index=False
        )
        etf_index_weights_df.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_indices_weights.csv', index=False
        )
        etf_returns.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_returns.csv', index=False
        )

class RegressionPricingModel(PricingModel):

    def __init__(self):
        super(RegressionPricingModel, self).__init__()

    # def calculate_products_returns_and_covariance(self, avg_risk_free_rate: float, current_risk_free_rate: float, last_date: datetime = datetime.today(), save_analytics: bool = False) -> (np.ndarray, np.ndarray):
    def calculate_products_returns_and_covariance(self, avg_risk_free_rate: float, current_risk_free_rate: float, last_date: datetime = datetime.today(), save_analytics: bool = False) -> (pd.DataFrame, pd.DataFrame):
        asset_class_returns, last_document_date = self.asset_class_repository.get_returns_in_time_horizon(self.settings.time_horizon, last_date)
        asset_class_market_caps = self.asset_class_repository.get_market_caps(datetime.now())

        asset_class_weights = self.__calculate_weights(asset_class_market_caps)

        risk_free_rates, last_document_date = self.risk_free_rate_repository.get_risk_free_rates_in_time_horizon(self.settings.time_horizon, last_document_date)

        etf_returns = self.etf_repository.get_returns_in_time_horizon(self.settings.time_horizon, last_document_date)

        asset_class_returns, risk_free_rates, etf_returns = util.synchronize_and_fill_dates([asset_class_returns, risk_free_rates, etf_returns])

        market_portfolio_returns = util.calculate_market_portfolio_returns(asset_class_returns, asset_class_weights)

        risk_free_rates_daily = util.get_daily_return(risk_free_rates)

        betas = self.__calculate_betas(etf_returns, market_portfolio_returns, risk_free_rates_daily)

        # arithmetic average excess return of GMP from paper "Historical Returns of the Market Portfolio"
        expected_market_portfolio_excess_return = util.get_daily_return(0.0398)
        # expected_market_portfolio_return_daily = np.mean(market_portfolio_returns)

        expected_products_excess_returns_daily = betas * expected_market_portfolio_excess_return
        expected_products_excess_returns = util.get_annual_return(expected_products_excess_returns_daily.to_numpy()).reshape(-1, 1)
        expected_products_returns = np.vstack((expected_products_excess_returns + current_risk_free_rate, current_risk_free_rate))

        etf_cov = util.get_annual_covariance(etf_returns)
        etf_cov = np.pad(etf_cov, pad_width=((0, 1), (0, 1)), mode='constant', constant_values=0)

        expected_returns_with_ter = util.include_ter(expected_products_returns)

        columns = self.metadata_repository.get_ISINs()

        if save_analytics:
            self.__save_model_analytics(util.get_last_year_and_month(last_date) + '/regression_pricing', expected_products_excess_returns, expected_products_returns, expected_returns_with_ter, etf_cov,
                                        betas, asset_class_weights, etf_returns, columns, asset_class_market_caps.columns)

        return pd.DataFrame(expected_returns_with_ter.reshape(1, -1), columns=columns), pd.DataFrame(etf_cov, columns=columns, index=columns)


    @staticmethod
    def __calculate_weights(market_caps: pd.DataFrame) -> np.ndarray:
        total_sum = market_caps.sum(axis=1)
        weights = market_caps.apply(lambda x: x / total_sum)
        return weights.to_numpy()

    @staticmethod
    def __calculate_beta(product_returns: np.ndarray, market_portfolio_returns: np.ndarray, risk_free_rates: np.ndarray) -> float:
        product_returns, market_portfolio_returns, risk_free_rates = product_returns.reshape(-1, 1), market_portfolio_returns.reshape(-1, 1), risk_free_rates.reshape(-1, 1)
        regression = LinearRegression(fit_intercept=True)
        regression.fit(market_portfolio_returns - risk_free_rates, product_returns - risk_free_rates)
        return regression.coef_.item()

    def __calculate_betas(self, etf_returns: pd.DataFrame, market_portfolio_returns: np.ndarray, risk_free_rates: pd.DataFrame) -> pd.DataFrame:
        betas = {}
        for product in etf_returns.columns:
            betas[product] = self.__calculate_beta(etf_returns[product].values, market_portfolio_returns, risk_free_rates.values)
        return pd.DataFrame(betas, index=['beta'])

    def __save_model_analytics(self, prefix: str, expected_excess_returns: np.ndarray, expected_returns: np.ndarray, expected_returns_with_ter: np.ndarray, etf_cov, betas: pd.DataFrame,
                               asset_class_weights: np.ndarray, etf_returns: pd.DataFrame, column_names: list[str], asset_classes: list[str]):
        directory_path = f'{self.settings.analytics_path}{prefix}/'
        os.makedirs(os.path.dirname(directory_path), exist_ok=True)

        expected_excess_returns = pd.DataFrame(expected_excess_returns.reshape(1, -1), columns=etf_returns.columns)

        expected_returns = pd.DataFrame(expected_returns.reshape(1, -1), columns=column_names)

        expected_returns_with_ter = pd.DataFrame(expected_returns_with_ter.reshape(1, -1), columns=column_names)

        etf_cov_df = pd.DataFrame(etf_cov, columns=column_names)

        asset_class_weights_df = pd.DataFrame(asset_class_weights.reshape(1, -1), columns=asset_classes)

        expected_excess_returns.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_expected_excess_returns.csv', index=False
        )
        expected_returns.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_expected_returns.csv', index=False
        )
        expected_returns_with_ter.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_expected_returns_with_ter.csv', index=False
        )
        etf_cov_df.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_covariance.csv', index=False
        )
        betas.to_csv(
            self.settings.analytics_path + f'{prefix}/betas.csv', index=False
        )
        asset_class_weights_df.to_csv(
            self.settings.analytics_path + f'{prefix}/asset_class_weights.csv', index=False
        )
        etf_returns.to_csv(
            self.settings.analytics_path + f'{prefix}/etf_returns.csv', index=False
        )
