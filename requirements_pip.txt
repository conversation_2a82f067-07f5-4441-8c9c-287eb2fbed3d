annotated-types @ file:///home/<USER>/feedstock_root/build_artifacts/annotated-types_1696634205638/work
anyio @ file:///C:/b/abs_847uobe7ea/croot/anyio_1706220224037/work
argon2-cffi @ file:///opt/conda/conda-bld/argon2-cffi_1645000214183/work
argon2-cffi-bindings @ file:///C:/ci_311/argon2-cffi-bindings_1676424443321/work
asttokens @ file:///opt/conda/conda-bld/asttokens_1646925590279/work
async-lru @ file:///C:/b/abs_e0hjkvwwb5/croot/async-lru_1699554572212/work
attrs @ file:///C:/b/abs_35n0jusce8/croot/attrs_1695717880170/work
Babel @ file:///C:/ci_311/babel_1676427169844/work
beautifulsoup4 @ file:///C:/b/abs_0agyz1wsr4/croot/beautifulsoup4-split_1681493048687/work
bleach @ file:///opt/conda/conda-bld/bleach_1641577558959/work
Bottleneck @ file:///C:/ci_311/bottleneck_1676500016583/work
certifi @ file:///C:/b/abs_35d7n66oz9/croot/certifi_1707229248467/work/certifi
cffi @ file:///C:/b/abs_924gv1kxzj/croot/cffi_1700254355075/work
charset-normalizer @ file:///tmp/build/80754af9/charset-normalizer_1630003229654/work
clarabel @ file:///D:/bld/clarabel_1697056757117/work
click @ file:///C:/b/abs_f9ihnt72pu/croot/click_1698129847492/work
colorama @ file:///C:/ci_311/colorama_1676422310965/work
comm @ file:///C:/ci_311/comm_1678376562840/work
contourpy @ file:///C:/b/abs_853rfy8zse/croot/contourpy_1700583617587/work
cvxopt==0.0.0
cvxpy @ file:///D:/bld/cvxpy-split_1705721175241/work
cycler @ file:///tmp/build/80754af9/cycler_1637851556182/work
debugpy @ file:///C:/b/abs_c0y1fjipt2/croot/debugpy_1690906864587/work
decorator @ file:///opt/conda/conda-bld/decorator_1643638310831/work
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
ecos @ file:///D:/bld/ecos_1707295504134/work
executing @ file:///opt/conda/conda-bld/executing_1646925071911/work
fastapi @ file:///C:/b/abs_17bc_xka63/croot/fastapi_1693295449860/work
fastjsonschema @ file:///C:/ci_311/python-fastjsonschema_1679500568724/work
fonttools==4.25.0
h11 @ file:///C:/b/abs_1czwoyexjf/croot/h11_1706652332846/work
idna @ file:///C:/ci_311/idna_1676424932545/work
ipykernel @ file:///C:/b/abs_c2u94kxcy6/croot/ipykernel_1705933907920/work
ipython @ file:///C:/b/abs_b6pfgmrqnd/croot/ipython_1704833422163/work
ipywidgets @ file:///C:/b/abs_5awapknmz_/croot/ipywidgets_1679394824767/work
jedi @ file:///C:/ci_311/jedi_1679427407646/work
Jinja2 @ file:///C:/b/abs_f7x5a8op2h/croot/jinja2_1706733672594/work
joblib @ file:///C:/b/abs_1anqjntpan/croot/joblib_1685113317150/work
json5 @ file:///tmp/build/80754af9/json5_1624432770122/work
jsonschema @ file:///C:/b/abs_d1c4sm8drk/croot/jsonschema_1699041668863/work
jsonschema-specifications @ file:///C:/b/abs_0brvm6vryw/croot/jsonschema-specifications_1699032417323/work
jupyter @ file:///C:/ci_311/jupyter_1678249952587/work
jupyter-console @ file:///C:/b/abs_82xaa6i2y4/croot/jupyter_console_1680000189372/work
jupyter-events @ file:///C:/b/abs_17ajfqnlz0/croot/jupyter_events_1699282519713/work
jupyter-lsp @ file:///C:/b/abs_ecle3em9d4/croot/jupyter-lsp-meta_1699978291372/work
jupyter_client @ file:///C:/b/abs_a6h3c8hfdq/croot/jupyter_client_1699455939372/work
jupyter_core @ file:///C:/b/abs_c769pbqg9b/croot/jupyter_core_1698937367513/work
jupyter_server @ file:///C:/b/abs_7esjvdakg9/croot/jupyter_server_1699466495151/work
jupyter_server_terminals @ file:///C:/b/abs_ec0dq4b50j/croot/jupyter_server_terminals_1686870763512/work
jupyterlab @ file:///C:/b/abs_43venm28fu/croot/jupyterlab_1706802651134/work
jupyterlab-pygments @ file:///tmp/build/80754af9/jupyterlab_pygments_1601490720602/work
jupyterlab-widgets @ file:///C:/b/abs_adrrqr26no/croot/jupyterlab_widgets_1700169018974/work
jupyterlab_server @ file:///C:/b/abs_e08i7qn9m8/croot/jupyterlab_server_1699555481806/work
kiwisolver @ file:///C:/ci_311/kiwisolver_1676431979301/work
MarkupSafe @ file:///C:/b/abs_ecfdqh67b_/croot/markupsafe_1704206030535/work
matplotlib @ file:///C:/b/abs_e26vnvd5s1/croot/matplotlib-suite_1698692153288/work
matplotlib-inline @ file:///C:/ci_311/matplotlib-inline_1676425798036/work
mistune @ file:///C:/ci_311/mistune_1676425111783/work
mkl-fft @ file:///C:/b/abs_19i1y8ykas/croot/mkl_fft_1695058226480/work
mkl-random @ file:///C:/b/abs_edwkj1_o69/croot/mkl_random_1695059866750/work
mkl-service==2.4.0
munkres==1.1.4
nbclient @ file:///C:/b/abs_cal0q5fyju/croot/nbclient_1698934263135/work
nbconvert @ file:///C:/b/abs_17p29f_rx4/croot/nbconvert_1699022793097/work
nbformat @ file:///C:/b/abs_5a2nea1iu2/croot/nbformat_1694616866197/work
nest-asyncio @ file:///C:/ci_311/nest-asyncio_1676423519896/work
notebook @ file:///C:/b/abs_26737osg4x/croot/notebook_1700582146311/work
notebook_shim @ file:///C:/b/abs_a5xysln3lb/croot/notebook-shim_1699455926920/work
numexpr @ file:///C:/b/abs_5fucrty5dc/croot/numexpr_1696515448831/work
numpy @ file:///C:/b/abs_16b2j7ad8n/croot/numpy_and_numpy_base_1704311752418/work/dist/numpy-1.26.3-cp311-cp311-win_amd64.whl#sha256=5f2c4b54fd5d52b9fb18e32607c79b03cf14665cecce8a5a10e2950559df4651
osqp @ file:///D:/bld/osqp_1696561025702/work
overrides @ file:///C:/b/abs_cfh89c8yf4/croot/overrides_1699371165349/work
packaging @ file:///C:/b/abs_28t5mcoltc/croot/packaging_1693575224052/work
pandas @ file:///C:/b/abs_fej9bi0gew/croot/pandas_1702318041921/work/dist/pandas-2.1.4-cp311-cp311-win_amd64.whl#sha256=d3609b7cc3e3c4d99ad640a4b8e710ba93ccf967ab8e5245b91033e0200f9286
pandocfilters @ file:///opt/conda/conda-bld/pandocfilters_1643405455980/work
parso @ file:///opt/conda/conda-bld/parso_1641458642106/work
patsy @ file:///home/<USER>/feedstock_root/build_artifacts/patsy_1704469236901/work
pillow @ file:///C:/b/abs_e22m71t0cb/croot/pillow_1707233126420/work
platformdirs @ file:///C:/b/abs_b6z_yqw_ii/croot/platformdirs_1692205479426/work
ply==3.11
prometheus-client @ file:///C:/ci_311/prometheus_client_1679591942558/work
prompt-toolkit @ file:///C:/b/abs_68uwr58ed1/croot/prompt-toolkit_1704404394082/work
psutil @ file:///C:/ci_311_rebuilds/psutil_1679005906571/work
pure-eval @ file:///opt/conda/conda-bld/pure_eval_1646925070566/work
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pydantic @ file:///home/<USER>/feedstock_root/build_artifacts/pydantic_1689770207113/work
pydantic-settings @ file:///home/<USER>/feedstock_root/build_artifacts/pydantic-settings_1699982624939/work
pydantic_core @ file:///D:/bld/pydantic-core_1689286485168/work
Pygments @ file:///C:/b/abs_fay9dpq4n_/croot/pygments_1684279990574/work
pymongo==3.12.0
pyparsing @ file:///C:/ci_311/pyparsing_1678502182533/work
PyQt5==5.15.10
PyQt5-sip @ file:///C:/b/abs_c0pi2mimq3/croot/pyqt-split_1698769125270/work/pyqt_sip
python-dateutil @ file:///tmp/build/80754af9/python-dateutil_1626374649649/work
python-dotenv @ file:///C:/ci_311/python-dotenv_1676455170580/work
python-json-logger @ file:///C:/b/abs_cblnsm6puj/croot/python-json-logger_1683824130469/work
pytz @ file:///C:/b/abs_19q3ljkez4/croot/pytz_1695131651401/work
pywin32==305.1
pywinpty @ file:///C:/ci_311/pywinpty_1677707791185/work/target/wheels/pywinpty-2.0.10-cp311-none-win_amd64.whl
PyYAML @ file:///C:/b/abs_782o3mbw7z/croot/pyyaml_1698096085010/work
pyzmq @ file:///C:/b/abs_89aq69t0up/croot/pyzmq_1705605705281/work
qdldl @ file:///D:/bld/qdldl-python_1702901193504/work
qtconsole @ file:///C:/b/abs_4awqjtg1ug/croot/qtconsole_1700160696631/work
QtPy @ file:///C:/b/abs_derqu__3p8/croot/qtpy_1700144907661/work
referencing @ file:///C:/b/abs_09f4hj6adf/croot/referencing_1699012097448/work
requests @ file:///C:/b/abs_474vaa3x9e/croot/requests_1707355619957/work
rfc3339-validator @ file:///C:/b/abs_ddfmseb_vm/croot/rfc3339-validator_1683077054906/work
rfc3986-validator @ file:///C:/b/abs_6e9azihr8o/croot/rfc3986-validator_1683059049737/work
rpds-py @ file:///C:/b/abs_76j4g4la23/croot/rpds-py_1698947348047/work
scikit-learn @ file:///C:/b/abs_38k7ridbgr/croot/scikit-learn_1684954723009/work
scipy==1.11.4
scs @ file:///D:/bld/scs_1707387246433/work
seaborn @ file:///home/<USER>/feedstock_root/build_artifacts/seaborn-split_1706340836595/work
Send2Trash @ file:///C:/b/abs_08dh49ew26/croot/send2trash_1699371173324/work
sip @ file:///C:/b/abs_edevan3fce/croot/sip_1698675983372/work
six @ file:///tmp/build/80754af9/six_1644875935023/work
sniffio @ file:///C:/b/abs_3akdewudo_/croot/sniffio_1705431337396/work
soupsieve @ file:///C:/b/abs_bbsvy9t4pl/croot/soupsieve_1696347611357/work
stack-data @ file:///opt/conda/conda-bld/stack_data_1646927590127/work
starlette @ file:///C:/b/abs_f6jqjvlnz0/croot/starlette-recipe_1692980584602/work
statsmodels @ file:///D:/bld/statsmodels_1702575579547/work
terminado @ file:///C:/ci_311/terminado_1678228513830/work
threadpoolctl @ file:///Users/<USER>/demo/mc3/conda-bld/threadpoolctl_1629802263681/work
tinycss2 @ file:///C:/ci_311/tinycss2_1676425376744/work
tornado @ file:///C:/b/abs_0cbrstidzg/croot/tornado_1696937003724/work
traitlets @ file:///C:/ci_311/traitlets_1676423290727/work
typing_extensions @ file:///C:/b/abs_72cdotwc_6/croot/typing_extensions_1705599364138/work
tzdata @ file:///croot/python-tzdata_1690578112552/work
urllib3 @ file:///C:/b/abs_a9n398cvep/croot/urllib3_1700840517182/work
uvicorn @ file:///C:/ci_311_rebuilds/uvicorn-split_1679017274842/work
wcwidth @ file:///Users/<USER>/demo/mc3/conda-bld/wcwidth_1629357192024/work
webencodings==0.5.1
websocket-client @ file:///C:/ci_311/websocket-client_1676426063281/work
widgetsnbextension @ file:///C:/b/abs_882k4_4kdf/croot/widgetsnbextension_1679313880295/work
