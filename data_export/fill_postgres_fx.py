import json
import psycopg2
from datetime import datetime

# Load JSON data
with open('dev.FX_rates_new_ts.json', 'r') as f:
    data = json.load(f)

# Connect to your Postgres database
conn = psycopg2.connect(
    dbname='financial_data_9zmf',
    user='valusense',
    password='lznTSU7Z6b2aUo1CBvTzjulrUziYn1Uv',
    host='dpg-d14q7tq4d50c73ck8bt0-a.frankfurt-postgres.render.com'
)
cur = conn.cursor()

# Prepare insert statement
insert_sql = """
INSERT INTO fx_rates (rate_date, base_currency, quote_currency, rate)
VALUES (%s, %s, %s, %s)
ON CONFLICT (rate_date, base_currency, quote_currency) DO NOTHING;
"""

for record in data:
    rate_date = record['date']['$date'][:10]  # 'YYYY-MM-DD'
    base_currency = 'EUR'
    rates = record['EUR']
    now = datetime.now()
    for quote_currency, rate in rates.items():
        cur.execute(insert_sql, (
            rate_date,
            base_currency,
            quote_currency,
            rate
        ))

conn.commit()
cur.close()
conn.close()
