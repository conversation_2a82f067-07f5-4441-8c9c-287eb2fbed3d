import pandas as pd
from pymongo import MongoClient
from datetime import datetime
import pytz

# Load CSV
df = pd.read_csv('all_data_for_all_listings.csv')

# Connect to MongoDB
client = MongoClient('mongodb+srv://valusense:<EMAIL>/')
db = client['dev']
collection = db['ETF_prices_original_new_ts']

# Parse the first OHLCV column to get instrument info
instruments = set()
for col in df.columns:
    if col == 'date':
        continue
    parts = col.split(':')
    if len(parts) == 4:
        isin, ticker, exchange, _ = parts
        instruments.add((isin, ticker, exchange))

# Prepare documents
documents = []
for _, row in df.iterrows():
    for isin, ticker, exchange in instruments:
        try:
            doc = {
                "date": pd.to_datetime(row['date']).replace(tzinfo=pytz.UTC),
                "instrument": {
                    "isin": isin,
                    "ticker": ticker,
                    "exchange_code": exchange
                },
                "open": row[f"{isin}:{ticker}:{exchange}:open"],
                "high": row[f"{isin}:{ticker}:{exchange}:high"],
                "low": row[f"{isin}:{ticker}:{exchange}:low"],
                "close": row[f"{isin}:{ticker}:{exchange}:close"],
                "adjusted_close": row[f"{isin}:{ticker}:{exchange}:adjusted_close"],
                "volume": row[f"{isin}:{ticker}:{exchange}:volume"]
            }
            if not any(pd.isna(v) for v in doc.values() if isinstance(v, (int, float))):
                documents.append(doc)

        except KeyError:
            print(f"Missing data for {isin}:{ticker}:{exchange}")
            continue

# Insert into MongoDB
if documents:
    collection.insert_many(documents)
    print(f"Successfully inserted {len(documents)} documents")
else:
    print("No valid documents to insert")
