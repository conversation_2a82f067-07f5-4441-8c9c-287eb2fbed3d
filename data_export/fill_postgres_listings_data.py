import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import numpy as np
import time

# Configuration
CSV_FILE = 'all_data_for_all_listings_with_returns.csv'
DB_PARAMS = {
    'dbname': 'financial_data_9zmf',
    'user': 'valusense',
    'password': 'lznTSU7Z6b2aUo1CBvTzjulrUziYn1Uv',
    'host': 'dpg-d14q7tq4d50c73ck8bt0-a.frankfurt-postgres.render.com'
}


def reshape_dataframe(df):
    """Melt DataFrame and keep only valid metric columns (with 3 colons)."""
    id_vars = ['date']
    value_vars = [col for col in df.columns if col.count(':') == 3]
    melted = df.melt(
        id_vars=id_vars,
        value_vars=value_vars,
        var_name='metric',
        value_name='value'
    ).dropna()
    return melted


def process_data(df):
    """Split metric column into isin, ticker, exchange, and field."""
    df[['isin', 'ticker', 'exchange', 'field']] = df['metric'].str.split(':', expand=True)
    pivoted = df.pivot_table(
        index=['date', 'isin', 'ticker', 'exchange'],
        columns='field',
        values='value'
    ).reset_index()
    # Rename 'return' to 'daily_return' if present
    if 'return' in pivoted.columns:
        pivoted = pivoted.rename(columns={'return': 'daily_return'})
    return pivoted


def get_asset_mappings(cur):
    """
    Fetch asset mappings, using only isin:ticker:exchange even if DB has currency.
    """
    cur.execute("SELECT id, asset_listing_code FROM asset_listings")
    mapping = {}
    for id, code in cur.fetchall():
        # Extract first 3 parts (ignore currency in DB code)
        key = ':'.join(code.split(':')[:3])
        mapping[key] = id
    return mapping


def prepare_insert_data(processed_df, asset_mappings):
    """
    Prepare data for bulk insert. Rename 'date' to 'data_date' for DB schema.
    """
    df = processed_df.copy().rename(columns={'date': 'data_date'})
    # Create asset_listing_code (without currency)
    df['asset_listing_code'] = df['isin'] + ':' + df['ticker'] + ':' + df['exchange']
    df['asset_listing_id'] = df['asset_listing_code'].map(asset_mappings)

    # Debug: Check mappings and missing values
    print("Sample asset_listing_code from DataFrame:", df['asset_listing_code'].head(3))
    print("Sample asset_listing_id from DB mapping:", df['asset_listing_id'].head(3))
    print("Non-null adjusted_close:", df['adjusted_close'].notnull().sum())
    print("Non-null daily_return:", df['daily_return'].notnull().sum())

    # Drop rows with missing critical fields
    df = df.dropna(subset=['asset_listing_id', 'adjusted_close', 'daily_return'])

    # Add empty EUR columns
    for col in ['open_eur', 'close_eur', 'high_eur', 'low_eur',
                'adjusted_close_eur', 'daily_return_eur']:
        df[col] = np.nan

    return df[[
        'data_date', 'asset_listing_id', 'open', 'close', 'high', 'low',
        'adjusted_close', 'volume', 'daily_return',
        'open_eur', 'close_eur', 'high_eur', 'low_eur',
        'adjusted_close_eur', 'daily_return_eur'
    ]]


def bulk_insert_data(cur, data):
    """Bulk insert using execute_values."""
    columns = ', '.join(data.columns)
    query = f"""
        INSERT INTO asset_listing_data ({columns})
        VALUES %s
    """
    execute_values(
        cur,
        query,
        data.to_numpy(),
        page_size=1000
    )


def optimized_parse_and_insert():
    start_time = time.time()
    df = pd.read_csv(CSV_FILE, parse_dates=['date'])

    melted = reshape_dataframe(df)

    processed = process_data(melted)

    with psycopg2.connect(**DB_PARAMS) as conn:
        with conn.cursor() as cur:
            asset_mappings = get_asset_mappings(cur)
            print("Sample DB asset codes:", list(asset_mappings.keys())[:5])

            insert_data = prepare_insert_data(processed, asset_mappings)
            print(f"Prepared {len(insert_data)} rows for insertion.")

            if not insert_data.empty:
                bulk_insert_data(cur, insert_data)
        conn.commit()

    end_time = time.time()
    print(f"Insert completed in {end_time - start_time:.2f} seconds.")


if __name__ == '__main__':
    optimized_parse_and_insert()
