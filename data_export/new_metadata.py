import pandas as pd
from pymongo import MongoClient
import re

def to_camel_case(s):
    s = re.sub(r'[^a-zA-Z0-9 ]', '', s)  # Remove non-alphanumeric except space
    parts = s.strip().split()
    if not parts:
        return ''
    return parts[0].lower() + ''.join(word.capitalize() for word in parts[1:])

def to_snake_case(s):
    # Remove non-alphanumeric except space
    s = re.sub(r'[^a-zA-Z0-9 ]', '', s)
    # Replace spaces with underscores and lowercase everything
    s = s.strip().replace(' ', '_').lower()
    return s

# Load ETF data
etf_df = pd.read_csv('etf_data_valid.csv')
etf_df.columns = [to_snake_case(col) for col in etf_df.columns]

# Load listings data
listings_df = pd.read_csv('filtered_listings_data.csv')
listings_df.columns = [to_snake_case(col) for col in listings_df.columns]

# Group listings by ISIN
listings_grouped = listings_df.groupby('isin').apply(
    lambda x: x[['stock_exchange', 'exchange_code', 'ticker', 'trade_currency']].to_dict('records')
).to_dict()

# Connect to MongoDB
client = MongoClient('mongodb+srv://valusense:<EMAIL>/')
db = client['dev']
collection = db['Metadata_new']

# Prepare and insert documents
docs = []
for isin, group in etf_df.groupby('isin'):
    base = group.iloc[0].to_dict()
    base['listings'] = listings_grouped.get(isin, [])
    docs.append(base)

if docs:
    collection.insert_many(docs)

print(f"Inserted {len(docs)} documents into MongoDB.")

