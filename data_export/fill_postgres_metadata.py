import json
import psycopg2
from datetime import datetime
from psycopg2.extras import execute_values


class DatabaseInserter:
    def __init__(self, db_params):
        self.conn = psycopg2.connect(**db_params)
        self.cur = self.conn.cursor()
        self._preload_references()

    def _preload_references(self):
        """Preload reference tables to memory"""
        self.cur.execute("SELECT id, name FROM asset_classes")
        self.asset_classes = {row[1]: row[0] for row in self.cur.fetchall()}

        self.cur.execute("SELECT code, name FROM currencies")
        self.currencies = {row[0]: row[0] for row in self.cur.fetchall()}

        self.cur.execute("SELECT eodhd_code, id FROM exchanges")
        self.exchanges = {row[0]: row[1] for row in self.cur.fetchall()}

    def get_or_create(self, table, name_field, value, additional_fields=None):
        """Get or create a reference table entry"""
        cache_map = {
            'asset_classes': self.asset_classes,
            'currencies': self.currencies,
            'exchanges': self.exchanges
        }

        # Check cache first
        if value in cache_map[table]:
            return cache_map[table][value]

        # Insert new reference
        query = f"INSERT INTO {table} ({name_field}{', ' + ', '.join(additional_fields.keys()) if additional_fields else ''}) VALUES (%s{', %s' * len(additional_fields) if additional_fields else ''}) RETURNING id"
        self.cur.execute(query, (value,) + tuple(additional_fields.values()) if additional_fields else (value,))
        new_id = self.cur.fetchone()[0]
        self.conn.commit()

        # Update cache
        cache_map[table][value] = new_id
        return new_id

    def process_assets(self, json_data):
        """Main processing function"""
        asset_inserts = []
        listing_inserts = []

        for asset in json_data:
            # Handle asset_metadata
            asset_class_id = self.get_or_create('asset_classes', 'name', asset['asset_class'])
            fund_currency_id = self.get_or_create('currencies', 'code', asset['fund_currency'])

            asset_data = (
                asset['isin'],
                asset['fund_name'],
                asset['ter'],
                asset.get('inception_date'),
                asset.get('fund_size_in_m'),
                asset['distribution_policy'],
                asset.get('replication'),
                asset.get('is_sustainable', False),
                asset_class_id,
                asset.get('index'),
                asset.get('investment_focus'),
                asset['legal_structure'],
                asset.get('strategy_risk'),
                fund_currency_id,
                asset.get('currency_risk'),
                None if asset.get('distribution_frequency') == '-' else asset.get('distribution_frequency'),
                asset['fund_domicile'],
                asset['fund_provider'],
                None,  # ibkr_link
            )

            # Create listings
            for listing in asset.get('listings', []):
                exchange_id = self.get_or_create('exchanges', 'exchange_code', listing['exchange_code'])
                trade_currency_id = self.get_or_create('currencies', 'code', listing['trade_currency'])

                listing_data = (
                    f"{asset['isin']}:{listing['ticker']}:{listing['exchange_code']}:{listing['trade_currency']}",
                    asset['isin'],
                    exchange_id,
                    listing['ticker'],
                    trade_currency_id,
                    False,  # is_representative
                    False,  # is_in_default_selection
                )
                listing_inserts.append(listing_data)

            asset_inserts.append(asset_data)

        return asset_inserts, listing_inserts

    def generate_sql(self, asset_data, listing_data):
        """Generate SQL insert statements"""
        # Insert asset_metadata
        execute_values(
            self.cur,
            """INSERT INTO asset_metadata (isin, fund_name, ter, inception_date, fund_size_in_m, distribution_policy,
                                           replication, is_sustainable, asset_class_id, index, investment_focus,
                                           legal_structure, strategy_risk, fund_currency, currency_risk,
                                           distribution_frequency, fund_domicile, fund_provider, ibkr_link)
               VALUES %s""",
            asset_data
        )

        # Insert asset_listings
        execute_values(
            self.cur,
            """INSERT INTO asset_listings (asset_listing_code, asset_isin, exchange_id, ticker, trade_currency,
                                           is_representative, is_in_default_selection)
               VALUES %s""",
            listing_data
        )

        self.conn.commit()


# Usage
if __name__ == "__main__":
    # Load JSON data
    with open('dev.Metadata_new.json') as f:
        data = json.load(f)

    # Database configuration
    db_params = {
        'dbname':'financial_data_9zmf',
        'user':'valusense',
        'password':'lznTSU7Z6b2aUo1CBvTzjulrUziYn1Uv',
        'host':'dpg-d14q7tq4d50c73ck8bt0-a.frankfurt-postgres.render.com'
    }

    # Process data
    inserter = DatabaseInserter(db_params)
    asset_data, listing_data = inserter.process_assets(data)

    # Generate and execute SQL
    inserter.generate_sql(asset_data, listing_data)
    inserter.conn.close()
