#%%
import pandas as pd
import numpy as np
import requests
from collections import Counter

from data_export.edit_etf_data import keep_columns, new_filtered_listings_data
#%%
def longest_zero_streak(df):
    longest_streaks = {}

    for col in df.columns:
        x = df[col] == 0.0  # True where value is exactly 0.0
        # Convert boolean to integer
        x = x.astype(int)
        # Assign group IDs for consecutive values
        group_ids = (x != x.shift()).cumsum()
        # Group by group ID and filter only those where value == 1 (i.e., streak of zeros)
        streaks = x.groupby(group_ids).sum()
        values = x.groupby(group_ids).first()
        zero_streaks = streaks[values == 1]
        # Record the max streak length for this column
        longest_streaks[col] = int(zero_streaks.max()) if not zero_streaks.empty else 0

    return pd.Series(longest_streaks)
#%%
def longest_zero_streak(df):
    # Create a boolean DataFrame: True where value is 0.0
    is_zero = df == 0.0
    # For each column, convert True/False to 1/0, then use numpy to find runs
    def max_run(x):
        # x is a boolean Series
        arr = x.values.astype(int)
        # Find where the value changes
        diff = np.diff(np.concatenate(([0], arr, [0])))
        # Start and end indices of runs of 1s
        run_starts = np.where(diff == 1)[0]
        run_ends = np.where(diff == -1)[0]
        # Lengths of runs
        run_lengths = run_ends - run_starts
        return run_lengths.max() if len(run_lengths) > 0 else 0
    return is_zero.apply(max_run)
#%%
stock_exchanges = ['LSE', 'AS', 'SW', 'XETRA', 'STU', 'PA', 'MC']
#%%
def extract_dates(json_data):
    dates = []

    # Extract from ExchangeHolidays
    if 'ExchangeHolidays' in json_data:
        for holiday in json_data['ExchangeHolidays'].values():
            if 'Date' in holiday:
                dates.append(holiday['Date'])

    return dates
#%%
holidays = {}

for stock_exchange in stock_exchanges:
    url = f'https://eodhd.com/api/exchange-details/{stock_exchange}?api_token=67052a6bbe4108.46809416&fmt=json&from=2022-05-23'
    response = requests.get(url)
    data = response.json()
    holidays[stock_exchange] = extract_dates(data)
#%%
all_listings_data = pd.read_csv('all_listings_data.csv')
#%%
all_listings_data.set_index('date', inplace=True)
#%%
all_listings_data
#%%
all_listings_data.fillna(value='ffill', inplace=True)
#%%
all_listings_data = all_listings_data.loc[:, ~all_listings_data.columns.duplicated()]
#%%
from collections import defaultdict

columns_by_exchange = defaultdict(list)

for col in all_listings_data.columns:
    parts = col.split('-')
    if len(parts) == 3:
        exch = parts[2]
        columns_by_exchange[exch].append(col)

dfs_by_exchange = {}
for exch, col_list in columns_by_exchange.items():
    valid_cols = [col for col in col_list if col in all_listings_data.columns]
    dfs_by_exchange[exch] = all_listings_data.loc[:, valid_cols]
#%%
for stock_exchange, stock_exchange_df in dfs_by_exchange.items():
    print(stock_exchange, stock_exchange_df.shape)
#%%
exchange_codes_in_order = [
    col.split('-')[2]
    for col in all_listings_data.columns
    if len(col.split('-')) == 3
]
#%%
dfs_by_exchange_without_holidays = {}

for stock_exchange in stock_exchanges:
    stock_exchange_df = dfs_by_exchange[stock_exchange]
    stock_exchange_holidays = holidays[stock_exchange]
    stock_exchange_df = stock_exchange_df[~stock_exchange_df.index.isin(stock_exchange_holidays)]
    dfs_by_exchange_without_holidays[stock_exchange] = stock_exchange_df
#%%
dfs_by_exchange_without_holidays['LSE']
#%%
dfs_by_exchange_without_holidays
#%%
zero_streaks_by_exchange = []
zeros_count_by_exchange = []
for stock_exchange, stock_exchange_df in dfs_by_exchange_without_holidays.items():
    stock_exchange_df = stock_exchange_df.apply(pd.to_numeric, errors='coerce')
    zero_streaks = longest_zero_streak(stock_exchange_df.pct_change().dropna())
    zeros_count_by_exchange.append((stock_exchange_df.pct_change().dropna() == 0.0).sum(axis=0))
    zero_streaks_by_exchange.append(zero_streaks)
#%%
zero_streaks_counters = []

for zero_streaks in zero_streaks_by_exchange:
    counted_zero_streaks = dict(sorted(Counter(list(zero_streaks)).items()))
    zero_streaks_counters.append(counted_zero_streaks)
#%%
zeros_counters = []

for zeros in zeros_count_by_exchange:
    counted_zeros = dict(sorted(Counter(list(zeros)).items()))
    zeros_counters.append(counted_zeros)
#%%
zero_streaks_counters
#%%
combined_zero_streaks_counter = Counter()
for d in zero_streaks_counters:
    combined_zero_streaks_counter.update(d)
#%%
combined_zeros_counter = Counter()
for d in zeros_counters:
    combined_zeros_counter.update(d)
#%%
sorted(dict(combined_zero_streaks_counter).items())
#%%
sorted(dict(combined_zeros_counter).items())
#%%
zero_streaks_by_exchange
#%%
keep_columns = []
for zero_streaks_exch in zero_streaks_by_exchange:
    keep_columns.extend(zero_streaks_exch[zero_streaks_exch <= 3].index.tolist())
#%%
len(keep_columns)
#%%
all_listings_data
#%%
all_listings_data[keep_columns]
#%%
subset_all_listings_data = all_listings_data[[col for col in all_listings_data.columns if col in keep_columns]]
#%%
subset_all_listings_data
#%%
subset_all_listings_data.to_csv('valid_listings_data.csv', index=True)
#
#%%
columns_valid_listings_data = subset_all_listings_data.columns.tolist()
#%%
len(columns_valid_listings_data)
#%%
columns_valid_listings_data
#%%
import re

pattern = r'\b[A-Z]{2}[A-Z0-9]{10}\b'

# Extract ISINs
isins = [re.search(pattern, item).group() for item in columns_valid_listings_data if re.search(pattern, item)]
#%%
isins
#%%
etf_data = pd.read_csv('etf_data_updated.csv', delimiter=';')
#%%
etf_data = etf_data.set_index('isin')
#%%
etf_data = etf_data.loc[isins]
#%%
etf_data
#%%
etf_data.to_csv('etf_data_valid.csv')

#%%
new_filtered_listings_data = pd.read_csv('new_filtered_listings_data.csv')
#%%
new_filtered_listings_data["combo"] = new_filtered_listings_data["ISIN"] + "-" + new_filtered_listings_data["Ticker"] + "-" + new_filtered_listings_data["EODHD Exchange Code"]
#%%
filtered_df = new_filtered_listings_data[new_filtered_listings_data["combo"].isin(columns_valid_listings_data)]
#%%
filtered_df
#%%
filtered_df.to_csv('filtered_listings_data.csv', index=False)

#%%
# Find duplicated combos (keep all duplicates, not just the 2nd+)
duplicates = filtered_df[filtered_df.duplicated("combo", keep=False)]

# Optional: sort by combo for easier inspection
duplicates = duplicates.sort_values("combo")

print(duplicates)
#%%
duplicates
#%%
remove_rows = list(duplicates.index)
#%%
remove_rows
#%%
good_list = [4926, 2385, 2387, 2389, 2386, 3898, 3908, 2733, 3761, 1945, 1947, 1951, 5677, 1398, 3347, 4035, 1842, 2059, 1835, 7, 4404, 2563, 4818, 3453, 1327, 2242, 4399, 2187, 3121, 577, 2349, 2303, 4210, 5229, 4175, 5573, 3314, 2842, 1514, 5775, 4439, 1572, 511, 2493, 2005, 2127, 3723, 1768]
#%%
for element in good_list:
    remove_rows.remove(element)
    print(element)
#%%
len(remove_rows)
#%%
filtered_df = filtered_df.drop(remove_rows)
#%%
filtered_df
#%%
duplicates = filtered_df[filtered_df.duplicated("combo", keep=False)]

# Optional: sort by combo for easier inspection
duplicates = duplicates.sort_values("combo")
#%%

#%%
duplicates
#%%
filtered_df.to_csv('filtered_listings_data.csv', index=False)
#%%
duplicates.nunique()

#%%
filtered_df['Trade Currency'].unique()
#%%
filtered_listings_data = pd.read_csv('filtered_listings_data.csv')
#%%
filtered_listings_data
#%%
filtered_listings_data['ticker.exchange'] = filtered_listings_data['Ticker'] + '.' + filtered_listings_data['Exchange Code']
print(filtered_listings_data['ticker.exchange'])
#%%
all_data = []
for idx, row in filtered_listings_data.iterrows():
    ticker = row['Ticker']
    exchange = row['EODHD Exchange Code']
    isin = row['ISIN']

    url = f'https://eodhd.com/api/eod/{ticker}.{exchange}?&period=d&api_token=67052a6bbe4108.46809416&fmt=json&from=2022-05-23'

    response = requests.get(url)

    if response.status_code == 200:
        data = response.json()
        df = pd.DataFrame(data)
        df = df[['date', 'adjusted_close']]
        col_name = f"{isin}:{ticker}:{exchange}"
        df.rename(columns={'adjusted_close': col_name}, inplace=True)
        df.set_index('date', inplace=True)
        all_data.append(df)
    else:
        print(f"Failed request for {ticker}.{exchange}, status code: {response.status_code}")

combined_df = pd.concat(all_data, axis=1)

combined_df.sort_index(inplace=True)
#%%
all_data = []
for idx, row in filtered_listings_data.iterrows():
    ticker = row['Ticker']
    exchange = row['Exchange Code']
    isin = row['ISIN']

    url = f'https://eodhd.com/api/eod/{ticker}.{exchange}?&period=d&api_token=67052a6bbe4108.46809416&fmt=json&from=2022-06-01'
    response = requests.get(url)

    if response.status_code == 200:
        data = response.json()
        df = pd.DataFrame(data)

        # Select all required columns
        df = df[['date', 'open', 'high', 'low', 'close', 'adjusted_close', 'volume']]

        # Create identifier prefix
        col_prefix = f"{isin}:{ticker}:{exchange}"

        # Rename columns with prefix
        df.rename(columns={
            col: f"{col_prefix}:{col}" for col in ['open', 'high', 'low', 'close', 'adjusted_close', 'volume']
        }, inplace=True)

        df.set_index('date', inplace=True)
        all_data.append(df)

new_combined_df = pd.concat(all_data, axis=1).sort_index()

#%%
new_combined_df.columns.is_unique
#%%
new_combined_df.to_csv('all_data_for_all_listings.csv', index=True)
