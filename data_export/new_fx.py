import pytz
import requests
import pandas as pd
from pymongo import MongoClient
from datetime import datetime

# Constants
TICKERS = [
    "EURUSD.FOREX", "EURGBP.FOREX", "EURCHF.FOREX",
    "EURJPY.FOREX", "EURCAD.FOREX", "EURAUD.FOREX", "EURSEK.FOREX", "EURHKD.FOREX"
]
TICKER_TO_CURRENCY = {
    "EURUSD.FOREX": "USD",
    "EURGBP.FOREX": "GBP",
    "EURCHF.FOREX": "CHF",
    "EURJPY.FOREX": "JPY",
    "EURCAD.FOREX": "CAD",
    "EURAUD.FOREX": "AUD",
    "EURSEK.FOREX": "SEK",
    "EURHKD.FOREX": "HKD"
}
API_TOKEN = '67052a6bbe4108.46809416'
API_BASE_URL = 'https://eodhd.com/api/eod'
FROM_DATE = '2022-06-01'
MONGO_URI = 'mongodb+srv://valusense:<EMAIL>/'
DB_NAME = 'dev'
COLLECTION_NAME = 'FX_rates_new_ts'


def fetch_fx_data(ticker: str) -> dict:
    url = f'{API_BASE_URL}/{ticker}?period=d&api_token={API_TOKEN}&fmt=json&from={FROM_DATE}'
    print(url)
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Failed to fetch data for {ticker}: {response.status_code}")
        return {}

    data = response.json()
    return {
        item['date']: (
            float(item['close']) if item['close'] not in ['NA', ''] else
            float(item['previousClose']) if item.get('previousClose') not in ['NA', ''] else
            0.0
        )
        for item in data
    }


def build_fx_dataframe() -> pd.DataFrame:
    combined_data = {}

    for ticker in TICKERS:
        currency = TICKER_TO_CURRENCY[ticker]
        fx_data = fetch_fx_data(ticker)
        combined_data[currency] = fx_data

    df = pd.DataFrame(combined_data)
    df.index = pd.to_datetime(df.index)
    df.sort_index(inplace=True)
    df.fillna(method='ffill', inplace=True)

    return df


def store_fx_to_mongo(df: pd.DataFrame):
    with MongoClient(MONGO_URI) as client:
        collection = client[DB_NAME][COLLECTION_NAME]

        for date, row in df.iterrows():
            eur_data = {}

            for currency in df.columns:
                eur_data[currency] = row[currency]

            # Add GBX if GBP is available
            if 'GBP' in row and pd.notna(row['GBP']):
                eur_data['GBX'] = round(row['GBP'] * 100, 4)

            record = {
                'date': date.to_pydatetime().replace(tzinfo=pytz.UTC),
                'EUR': eur_data
            }

            collection.insert_one(record)


def main():
    df = build_fx_dataframe()
    store_fx_to_mongo(df)


if __name__ == '__main__':
    main()