import json
import psycopg2

# Path to your JSON file
JSON_FILE = 'dev.Risk_free_rates.json'

# The rate_name to use for all rows
RATE_NAME = '€STR'

# Connect to PostgreSQL
conn = psycopg2.connect(
    dbname='financial_data_9zmf',
    user='valusense',
    password='lznTSU7Z6b2aUo1CBvTzjulrUziYn1Uv',
    host='dpg-d14q7tq4d50c73ck8bt0-a.frankfurt-postgres.render.com'
)
cur = conn.cursor()

# Load JSON data
with open(JSON_FILE, 'r', encoding='utf-8') as f:
    data = json.load(f)

# Prepare and execute insert statements
for entry in data:
    date_str = entry['date']['$date'][:10]  # Extract 'YYYY-MM-DD'
    rate = entry['rate']
    if isinstance(rate, dict):
        rate = float(rate.get('$numberDouble', 0.0))
    else:
        rate = float(rate)
    cur.execute(
        """
        INSERT INTO risk_free_rates (rate_date, rate, rate_name)
        VALUES (%s, %s, %s)
        ON CONFLICT (rate_date) DO NOTHING
        """,
        (date_str, rate, RATE_NAME)
    )

conn.commit()
cur.close()
conn.close()
print("Data inserted into risk_free_rates table.")
