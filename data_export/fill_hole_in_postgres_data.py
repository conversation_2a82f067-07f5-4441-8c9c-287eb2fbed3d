import os
import sys
import logging
import dotenv
import requests
import psycopg2
import time
from psycopg2.extras import execute_values, DictCursor
from datetime import datetime, timedelta, timezone, date
from fx_mappings import FX_TICKERS, TICKER_TO_CURRENCY

dotenv.load_dotenv()

# --- Logger Setup ---
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
ch.setFormatter(formatter)
logger.addHandler(ch)

EODHD_TOKEN = os.environ['EODHD_TOKEN']
DISCORD_WEBHOOK_URL = os.environ.get('DISCORD_WEBHOOK_URL')
EODHD_HISTORICAL_URL = "https://eodhd.com/api/eod"

# --- DB Connection ---
def get_db_connection():
    return psycopg2.connect(
        dbname=os.environ['DB_NAME_POSTGRES'],
        user=os.environ['DB_USER_POSTGRES'],
        password=os.environ['DB_PASSWORD_POSTGRES'],
        host=os.environ['DB_HOST_POSTGRES'],
        port=os.environ['DB_PORT_POSTGRES'],
        options=f"-c search_path={os.getenv('DB_SCHEMA', 'test')}",
        cursor_factory=DictCursor
    )

# --- Notification Stub ---
def send_discord_notification(msg):
    return
    # if not DISCORD_WEBHOOK_URL:
    #     return
    # try:
    #     requests.post(DISCORD_WEBHOOK_URL, json={"content": msg, "username": "Error Bot"}, timeout=10)
    # except Exception as e:
    #     logger.error(f"Discord notification error: {str(e)}")

# --- Fetch JSON Helper ---
def fetch_json(url, params, label=None):
    try:
        resp = requests.get(url, params=params, timeout=30)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        msg = f"{label or url}: Request failed: {e}"
        logger.error(msg)
        send_discord_notification(msg)
        return None

# --- Asset Listings ---
def get_all_asset_listings(conn):
    with conn.cursor() as cur:
        cur.execute("""
            SELECT al.id, al.ticker, ex.eodhd_code, al.trade_currency
            FROM asset_listings al
            JOIN exchanges ex ON al.exchange_id = ex.id
        """)
        return [
            {
                'id': row['id'],
                'ticker': row['ticker'],
                'exchange_code': row['eodhd_code'],
                'trade_currency': row['trade_currency']
            }
            for row in cur.fetchall()
        ]

def get_last_date_for_asset(conn, asset_id):
    with conn.cursor() as cur:
        cur.execute("""
            SELECT MAX(data_date) 
            FROM asset_listing_data 
            WHERE asset_listing_id = %s
        """, (asset_id,))
        result = cur.fetchone()
        return result[0] if result else None

# --- FX Historical Fetch ---
def fetch_fx_historical(ticker, from_date, to_date):
    params = {
        "api_token": EODHD_TOKEN,
        "fmt": "json",
        "from": from_date.strftime("%Y-%m-%d"),
        "to": to_date.strftime("%Y-%m-%d")
    }
    return fetch_json(f"{EODHD_HISTORICAL_URL}/{ticker}", params, label=f"FX History for {ticker}")

def build_fx_rates_by_date(fx_data, ticker):
    fx_rates_by_date = {}
    currency = TICKER_TO_CURRENCY.get(ticker)
    if not currency:
        return fx_rates_by_date
    for entry in fx_data:
        date_str = entry.get('date')
        if not date_str:
            continue
        rate_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        rate = entry.get('adjusted_close') or entry.get('close')
        try:
            rate_float = float(rate)
        except (TypeError, ValueError):
            continue
        if rate_date not in fx_rates_by_date:
            fx_rates_by_date[rate_date] = {}
        fx_rates_by_date[rate_date][currency] = rate_float
    return fx_rates_by_date

# --- Asset Price Historical Fetch ---
def fetch_asset_historical(ticker, exchange_code, from_date, to_date):
    symbol = f"{ticker}.{exchange_code}"
    params = {
        "api_token": EODHD_TOKEN,
        "fmt": "json",
        "from": from_date.strftime("%Y-%m-%d"),
        "to": to_date.strftime("%Y-%m-%d")
    }
    return fetch_json(f"{EODHD_HISTORICAL_URL}/{symbol}", params, label=f"History for {symbol}")

# --- Save FX Rates ---
def save_fx_rates(cur, fx_rates_by_date):
    fxRatesToInsert = []
    for rate_date, rates in fx_rates_by_date.items():
        for currency, rate in rates.items():
            fxRatesToInsert.append((rate_date, 'EUR', currency, rate))
            if currency == 'GBP':
                gbx_rate = rate * 100
                fxRatesToInsert.append((rate_date, 'EUR', 'GBX', gbx_rate))
    if fxRatesToInsert:
        execute_values(
            cur,
            """
            INSERT INTO fx_rates (rate_date, base_currency, quote_currency, rate)
            VALUES %s 
            ON CONFLICT (rate_date, base_currency, quote_currency)
            DO NOTHING
            """,
            fxRatesToInsert
        )
        logger.info(f"Inserted {len(fxRatesToInsert)} FX rates")

# --- Save Asset Listing Prices ---
def save_asset_listing_prices(conn, listing, price_data, fx_rates_by_date):
    with conn.cursor() as cur:
        asset_id = listing['id']
        trade_currency = listing['trade_currency']
        # Fetch previous closes for daily return calculation
        cur.execute("""
            SELECT DISTINCT ON (asset_listing_id) asset_listing_id,
                                                      adjusted_close_eur,
                                                      adjusted_close
            FROM asset_listing_data
            WHERE asset_listing_id = %s
            ORDER BY asset_listing_id, data_date DESC
        """, (asset_id,))
        row = cur.fetchone()
        prev_adj_close_eur = float(row['adjusted_close_eur']) if row and row['adjusted_close_eur'] is not None else None
        prev_adj_close = float(row['adjusted_close']) if row and row['adjusted_close'] is not None else None

        to_insert = []
        price_data_sorted = sorted(price_data, key=lambda x: x['date'])
        for asset in price_data_sorted:
            # Extract all price fields
            open_val = asset.get('open')
            high_val = asset.get('high')
            low_val = asset.get('low')
            close_val = asset.get('close') or asset.get('prev_close')
            adjusted_close_val = asset.get('adjusted_close')
            volume_val = asset.get('volume')
            date_str = asset.get('date')
            price_date = datetime.strptime(date_str, "%Y-%m-%d").date() if date_str else None
            if not price_date:
                continue
            try:
                open_val = float(open_val) if open_val not in ('', 'NA', None) else None
                high_val = float(high_val) if high_val not in ('', 'NA', None) else None
                low_val = float(low_val) if low_val not in ('', 'NA', None) else None
                close_val = float(close_val) if close_val not in ('', 'NA', None) else None
                adjusted_close_val = float(adjusted_close_val) if adjusted_close_val not in ('', 'NA', None) else None
                volume_val = int(volume_val) if volume_val not in ('', 'NA', None) else None
            except (TypeError, ValueError) as e:
                logger.error(f"Value conversion error for {listing['ticker']}: {e}")
                continue

            # FX conversion
            fx_rates = fx_rates_by_date.get(price_date, {})
            try:
                if trade_currency == 'EUR':
                    conversion_factor = 1.0
                elif trade_currency in fx_rates:
                    conversion_factor = fx_rates[trade_currency]
                elif trade_currency == 'GBX':
                    if 'GBP' in fx_rates:
                        conversion_factor = fx_rates['GBP'] * 100.0
                    else:
                        logger.error(f"No FX rate for GBX on {price_date}")
                        continue
                else:
                    logger.error(f"No FX rate for {trade_currency} on {price_date}")
                    continue

                open_eur = open_val / conversion_factor if open_val is not None else None
                high_eur = high_val / conversion_factor if high_val is not None else None
                low_eur = low_val / conversion_factor if low_val is not None else None
                close_eur = close_val / conversion_factor if close_val is not None else None
                adjusted_close_eur = adjusted_close_val / conversion_factor if adjusted_close_val is not None else None
            except Exception as e:
                logger.error(f"FX conversion error for {listing['ticker']}: {e}")
                continue

            # Daily return (EUR)
            daily_return_eur = None
            if adjusted_close_eur is not None and prev_adj_close_eur is not None:
                try:
                    daily_return_eur = (adjusted_close_eur - prev_adj_close_eur) / prev_adj_close_eur
                except (TypeError, ZeroDivisionError):
                    daily_return_eur = None

            # Daily return (original)
            daily_return = None
            if adjusted_close_val is not None and prev_adj_close is not None:
                try:
                    daily_return = (adjusted_close_val - prev_adj_close) / prev_adj_close
                except (TypeError, ZeroDivisionError):
                    daily_return = None

            to_insert.append((
                asset_id,          # asset_listing_id
                price_date,        # data_date
                open_val,          # open
                high_val,          # high
                low_val,           # low
                close_val,         # close
                adjusted_close_val,     # adjusted_close
                volume_val,        # volume
                open_eur,          # open_eur
                high_eur,          # high_eur
                low_eur,           # low_eur
                close_eur,         # close_eur
                adjusted_close_eur,     # adjusted_close_eur
                round(daily_return, 8) if daily_return is not None else None,
                round(daily_return_eur, 8) if daily_return_eur is not None else None
            ))

            # Update previous for next day's return calculation
            prev_adj_close_eur = adjusted_close_eur
            prev_adj_close = adjusted_close_val

        if to_insert:
            try:
                execute_values(
                    cur,
                    """
                    INSERT INTO asset_listing_data (asset_listing_id, data_date,
                                                    open, high, low, close, adjusted_close, volume,
                                                    open_eur, high_eur, low_eur, close_eur, adjusted_close_eur,
                                                    daily_return, daily_return_eur)
                    VALUES %s
                    ON CONFLICT (asset_listing_id, data_date)
                    DO NOTHING
                    """,
                    to_insert
                )
                logger.info(f"Inserted {len(to_insert)} price records for {listing['ticker']}")
            except psycopg2.errors.NumericValueOutOfRange as e:
                logger.error(f"Numeric overflow error during insert: {e}")
                raise
            except Exception as e:
                logger.error(f"Database insert failed: {e}")
                raise

# --- ESTR Fetch and Save ---
def fetch_latest_estr():
    try:
        response = requests.get("https://api.estr.dev/historical", timeout=30)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.error(f"Failed to fetch ESTR: {e}")
        send_discord_notification(f"Failed to fetch ESTR: {e}")
        return None

def save_risk_free_rate(cur, estr_data):
    try:
        for d in estr_data:
            rate_date = datetime.strptime(d['date'], '%Y-%m-%d').date()
            rate_value = float(d['value']) / 100
            cur.execute("""
                        INSERT INTO risk_free_rates (rate_date, rate, rate_name)
                        VALUES (%s, %s, '€STR')
                        ON CONFLICT (rate_date) DO NOTHING
                        """, (rate_date, rate_value))
    except (ValueError, KeyError) as e:
        logger.error(f"Error processing risk-free rate: {str(e)}")

# --- Main ---
def main():
    try:
        logger.info('Starting historical data fetch')
        with get_db_connection() as conn:
            asset_listings = get_all_asset_listings(conn)
            if not asset_listings:
                logger.error("No asset listings found")
                return

            to_date = date.today() - timedelta(days=1)
            # Find the earliest missing date among all assets
            from_dates = []
            for a in asset_listings:
                last_date = get_last_date_for_asset(conn, a['id'])
                from_dates.append((last_date + timedelta(days=1)) if last_date else date(2022, 6, 2))
            from_date = min(from_dates)
            if from_date > to_date:
                logger.info("All assets are up-to-date")
                return

            # --- Fetch FX rates for the date range ---
            fx_rates_by_date = {}
            for ticker in FX_TICKERS:
                logger.info(f"Fetching FX rates for {ticker} from {from_date} to {to_date}")
                fx_data = fetch_fx_historical(ticker, from_date, to_date) or []
                rates_by_date = build_fx_rates_by_date(fx_data, ticker)
                # Merge into master dict
                for d, rates in rates_by_date.items():
                    if d not in fx_rates_by_date:
                        fx_rates_by_date[d] = {}
                    fx_rates_by_date[d].update(rates)
                # time.sleep(0.5)  # Rate limit

            # --- Save FX rates to DB ---
            with conn.cursor() as cur:
                save_fx_rates(cur, fx_rates_by_date)
                conn.commit()

            # --- Fetch and save asset prices ---
            for listing in asset_listings:
                asset_id = listing['id']
                last_date = get_last_date_for_asset(conn, asset_id)
                asset_from_date = (last_date + timedelta(days=1)) if last_date else from_date
                if asset_from_date > to_date:
                    continue  # Already up-to-date

                logger.info(f"Fetching prices for {listing['ticker']} from {asset_from_date} to {to_date}")
                price_data = fetch_asset_historical(
                    listing['ticker'],
                    listing['exchange_code'],
                    asset_from_date,
                    to_date
                ) or []
                if price_data:
                    save_asset_listing_prices(conn, listing, price_data, fx_rates_by_date)
                    conn.commit()
                # time.sleep(0.5)  # Rate limit

            # --- Fetch and save ESTR ---
            logger.info("Fetching €STR rate")
            estr_data = fetch_latest_estr()
            if estr_data:
                with conn.cursor() as cur:
                    save_risk_free_rate(cur, estr_data)
                conn.commit()
                logger.info("Inserted risk-free rates")
            else:
                logger.warning("No ESTR data fetched")

            logger.info('Historical data update completed')

    except Exception as e:
        logger.error(f"Critical error: {str(e)}", exc_info=True)
        send_discord_notification(f"Critical error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
