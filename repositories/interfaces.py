from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Tuple

import pandas as pd


class EtfRepositoryInterface(ABC):
    """
    Interface for ETF repositories.
    Implementations should handle ETF data retrieval and storage operations.
    """

    @abstractmethod
    def get_latest_prices_eur(
            self,
            last_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Get the latest prices in EUR for ETFs.
        
        Args:
            last_date: Optional date to get prices up to
            
        Returns:
            DataFrame with prices
        """
        pass

    @abstractmethod
    def get_latest_prices_eur_for_asset_listing_codes(
            self,
            asset_listing_codes: List[str],
            last_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Retrieve the latest EUR prices for the given asset listing codes up to the specified date.

        Args:
            asset_listing_codes (List[str]): List of asset listing codes.
            last_date (Optional[datetime]): The last date to consider for prices.

        Returns:
            pd.DataFrame: A DataFrame with dates as index and asset listing codes as columns.
        """
        pass

    @abstractmethod
    def get_returns_eur_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Get returns for ETFs within a specified time horizon.
        
        Args:
            time_horizon_in_years: Number of years to look back
            last_date: Optional end date for the time horizon
            
        Returns:
            DataFrame with returns
        """
        pass

    @abstractmethod
    def save_simulated_returns(
            self,
            simulated_returns: pd.DataFrame
    ) -> None:
        """
        Save simulated returns for ETFs.
        
        Args:
            simulated_returns: DataFrame with simulated returns
        """
        pass

    @abstractmethod
    def get_simulations_for_asset_listing_codes(
            self,
            asset_listing_codes: List[str],
            n_simulations: Optional[int] = None
    ) -> pd.DataFrame:
        """
        Retrieve simulation results for the given asset listing codes.

        Args:
            asset_listing_codes (List[str]): List of asset listing codes.
            n_simulations (Optional[int]): Number of simulations to retrieve.

        Returns:
            pd.DataFrame: Simulation results as a DataFrame.
        """
        pass


class MetadataRepositoryInterface(ABC):
    """
    Interface for metadata repositories.
    Implementations should handle asset metadata retrieval operations.
    """

    @abstractmethod
    def get_metadata_for_ISINs(
            self,
            ISINs: Optional[List[str]] = None
    ):
        """
        Get metadata for specified ISINs.
        
        Args:
            ISINs: Optional list of ISINs to get metadata for
            
        Returns:
            List of metadata objects
        """
        pass

    @abstractmethod
    def get_fields_for_ISINs(self, ISINs: List[str], fields: List[str]) -> Dict[str, List]:
        """
        Get specific fields for specified ISINs.
        
        Args:
            ISINs: List of ISINs to get fields for
            fields: List of field names to retrieve
            
        Returns:
            Dictionary mapping field names to lists of values
        """
        pass

    @abstractmethod
    def get_asset_class_ISINs(self) -> Dict[str, List[str]]:
        """
        Get ISINs grouped by asset class.
        
        Returns:
            Dictionary mapping asset class names to lists of ISINs
        """
        pass

    @abstractmethod
    def get_ter_for_all_listings(self) -> Dict[str, float]:
        """
        Retrieve the Total Expense Ratio (TER) for listings.

        Returns:
            Dict[str, float]: Mapping from asset listing code to TER.
        """
        pass


class AssetClassRepositoryInterface(ABC):
    """
    Interface for asset class repositories.
    Implementations should handle asset class data retrieval operations.
    """

    @abstractmethod
    def get_market_caps(
            self,
            last_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Retrieve the latest market capitalizations for each asset class as of the specified date.

        Args:
            last_date (Optional[datetime]): The last date to consider for market caps.

        Returns:
            pd.DataFrame: A DataFrame with dates as index, asset class names as columns, and market caps as values.
        """
        pass

    @abstractmethod
    def get_returns_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: Optional[datetime] = None
    ) -> Tuple[pd.DataFrame, datetime]:
        """
        Retrieve daily returns for representative asset listings within a specified time horizon up to a given date.

        Args:
            time_horizon_in_years (int): Number of years for the time horizon.
            last_date (Optional[datetime]): The last date to consider for returns.

        Returns:
            Tuple[pd.DataFrame, datetime]: DataFrame of returns and the last document date.
        """
        pass


class RiskFreeRateRepositoryInterface(ABC):
    """
    Interface for risk-free rate repositories.
    Implementations should handle risk-free rate data retrieval operations.
    """

    def get_risk_free_rates_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: Optional[datetime] = None
    ) -> Tuple[pd.DataFrame, datetime]:
        """
        Retrieve risk-free rates within a specified time horizon up to a given date.

        Args:
            time_horizon_in_years (int): Number of years for the time horizon.
            last_date (Optional[datetime]): The last date to consider for rates.

        Returns:
            Tuple[pd.DataFrame, datetime]: DataFrame of rates and the last document date.
        """
        pass

    @abstractmethod
    def get_avg_risk_free_rate_in_time_horizon(
            self,
            time_horizon_in_years: int,
            last_date: Optional[datetime] = None
    ) -> float:
        """
        Retrieve the average risk-free rate over a specified time horizon up to a given date.

        Args:
            time_horizon_in_years (int): Number of years for the time horizon.
            last_date (Optional[datetime]): The last date to consider for rates.

        Returns:
            float: The average risk-free rate.
        """
        pass

    @abstractmethod
    def get_last_risk_free_rate(
            self,
            last_date: Optional[datetime] = None
    ) -> float:
        """
        Retrieve the most recent risk-free rate up to a given date.

        Args:
            last_date (Optional[datetime]): The last date to consider for rates.

        Returns:
            float: The most recent risk-free rate.
        """
        pass
