from datetime import datetime
from pymongo import MongoClient
import pandas as pd
from dateutil.relativedelta import relativedelta

from configuration import config


class AssetClassesRepository:
    """
    Repository for interacting with asset class data stored in MongoDB.

    Attributes:
        settings: Configuration settings containing database connection details.
    """

    def __init__(self):
        self.settings = config.get_settings()

    def get_market_caps(self, last_date: datetime = None) -> pd.DataFrame:
        """
        Fetches the latest market capitalization data up to the specified date.

        Args:
            last_date (datetime, optional): The upper bound for the data. Defaults to the current datetime.

        Returns:
            pd.DataFrame: A DataFrame containing market capitalization data.

        Raises:
            ValueError: If no data is found for the given date range.
        """

        last_date = last_date or datetime.now()

        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            market_caps_collection = db[self.settings.market_caps_collection]

            try:
                market_caps = market_caps_collection.find({
                    'date': {'$lte': last_date}
                },
                    {'_id': False, 'date': False}
                ).sort('date', -1).limit(1).next()
            except StopIteration:
                raise ValueError(f"No market cap data found for date <= {last_date}.")

            return pd.DataFrame(market_caps, index=[0])

    def get_returns_in_time_horizon(self,
                                    time_horizon_in_years: int,
                                    last_date: datetime = None
                                    ) -> (pd.DataFrame, datetime):
        """
        Fetches returns data within a specified time horizon.

        Args:
            time_horizon_in_years (int): The length of the time horizon in years.
            last_date (datetime, optional): The upper bound for the data. Defaults to the current datetime.

        Returns:
            tuple:
                - pd.DataFrame: A DataFrame containing returns data within the time horizon.
                - datetime: The date of the last document in the dataset.

        Raises:
            ValueError: If no data is found for the given date range.
        """

        last_date = last_date or datetime.now()

        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            asset_class_collection = db[self.settings.asset_class_returns_collection]

            try:
                last_document = asset_class_collection.find({
                    'date': {'$lte': last_date}
                },
                    {'_id': False}
                ).sort('date', -1).limit(1).next()
            except StopIteration:
                raise ValueError(f"No return data found for date <= {last_date}.")

            last_document_date = last_document['date']
            start_date = last_document_date - relativedelta(years=time_horizon_in_years)

            returns_cursor = asset_class_collection.find({
                "date": {
                    "$gte": start_date,
                    "$lte": last_document_date
                }
            },
                {'_id': False}
            ).sort('date', 1)

            returns_within_time_horizon = list(returns_cursor)
            if not returns_within_time_horizon:
                raise ValueError(f"No return data found between {start_date} and {last_document_date}.")

            returns_df = pd.DataFrame(returns_within_time_horizon)
            returns_df.set_index('date', inplace=True)

            return returns_df, last_document_date
