from datetime import datetime
from typing import Optional
from pymongo import MongoClient
import pandas as pd

from configuration import config


class EtfIndicesRepository:
    """
    Repository for interacting with ETF index market capitalization data in MongoDB.

    Attributes:
        settings: Configuration settings containing database connection details.
    """

    def __init__(self):
        self.settings = config.get_settings()


    def get_market_caps(self, last_date: Optional[datetime] = None) -> pd.DataFrame:
        """
                Fetches the latest market capitalization data up to the specified date.

                Args:
                    last_date (datetime, optional): The upper bound for the data. Defaults to the current datetime.

                Returns:
                    pd.DataFrame: A DataFrame with ISIN values as columns and a single row indexed by date.

                Raises:
                    ValueError: If no market capitalization data is found or required fields are missing.
                """

        last_date = last_date or datetime.now()

        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            market_caps_collection = db[self.settings.index_market_caps_collection]

            try:
                market_caps = market_caps_collection.find({
                    'date': {'$lte': last_date}
                },
                    {'_id': False}
                ).sort('date', -1).limit(1).next()
            except StopIteration:
                raise ValueError(f"No market cap data found for date <= {last_date}.")

            if 'isin_values' not in market_caps or 'date' not in market_caps:
                raise ValueError("Missing required fields ('isin_values' or 'date') in the market cap data.")

            isin_values = market_caps['isin_values']

            market_caps_df = pd.DataFrame(isin_values, index=[market_caps['date']])
            market_caps_df.index.name = 'date'

            return market_caps_df