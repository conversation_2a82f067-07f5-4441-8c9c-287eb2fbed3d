from datetime import datetime
from dateutil.relativedelta import relativedelta
from typing import <PERSON>ple, Optional

from pymongo import MongoClient
import pandas as pd

from configuration import config


class RiskFreeRateRepository:
    def __init__(self):
        self.settings = config.get_settings()

    def get_risk_free_rates_in_time_horizon(self,
                                            time_horizon_in_years: int,
                                            last_date: Optional[datetime] = None
                                            ) -> Tuple[pd.DataFrame, datetime]:
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            risk_free_rates_collection = db[self.settings.risk_free_rates_collection]

            if not last_date:
                last_date = datetime.now()

            last_document = risk_free_rates_collection.find({
                'date': {'$lte': last_date}
            },
                {'_id': False}
            ).sort('date', -1).limit(1).next()

            if not last_document:
                raise ValueError("No risk-free rate data found before the specified date.")

            last_document_date = last_document['date']
            start_date = last_document_date - relativedelta(years=time_horizon_in_years)

            rates_cursor = risk_free_rates_collection.find({
                'date': {
                    '$gte': start_date,
                    '$lte': last_document_date
                }
            },
                {'_id': False}
            ).sort('date', 1)

            risk_free_rates_df = pd.DataFrame(rates_cursor)
            risk_free_rates_df.set_index('date', inplace=True)

            return risk_free_rates_df, last_document_date

    def get_avg_risk_free_rate_in_time_horizon(self,
                                               time_horizon_in_years: int,
                                               last_date: Optional[datetime] = None
                                               ) -> float:
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            risk_free_rates_collection = db[self.settings.risk_free_rates_collection]

            if not last_date:
                last_date = datetime.now()

            last_document = risk_free_rates_collection.find({
                'date': {'$lte': last_date}
            },
                {'_id': False}
            ).sort('date', -1).limit(1).next()

            last_document_date = last_document['date']
            start_date = last_document_date - relativedelta(years=time_horizon_in_years)

            aggregation_pipeline = [
                {
                    '$match': {
                        'date': {
                            '$gte': start_date,
                            '$lte': last_document_date
                        }
                    }
                },
                {
                    '$group': {
                        '_id': None,
                        'average_rate': {'$avg': '$rate'}
                    }
                },
                {'$project':{
                    '_id': 0,
                    'average_rate': 1
                }}
            ]

            result = list(risk_free_rates_collection.aggregate(aggregation_pipeline))

            if result:
                avg_risk_free_rate = result[0]['average_rate']
            else:
                avg_risk_free_rate = 0.0

            return avg_risk_free_rate

    def get_last_risk_free_rate(self, last_date: Optional[datetime] = None) -> float:
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            risk_free_rates_collection = db[self.settings.risk_free_rates_collection]

            if last_date is None:
                last_date = datetime.now()

            last_document = risk_free_rates_collection.find({
                'date': {'$lte': last_date}
            },
                {'_id': False}
            ).sort('date', -1).limit(1).next()

            return last_document['rate']

    def get_risk_free_rate_for_dates(self, dates_df: pd.DataFrame) -> float:
        dates_list = dates_df['date'].tolist()

        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            risk_free_rates_collection = db[self.settings.risk_free_rates_collection]

            pipeline = [
                {'$match': {'date': {'$in': dates_list}}},
                {
                    '$group': {
                        '_id': None,
                        'averageRate': {'$avg': '$rate'}
                    }
                }
            ]

            aggregation_result = list(risk_free_rates_collection.aggregate(pipeline))

        return aggregation_result[0]['averageRate'] if aggregation_result else 0