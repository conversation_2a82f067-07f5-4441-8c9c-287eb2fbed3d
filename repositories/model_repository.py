import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Optional, Tuple

import pymongo
from pymongo import MongoClient

from configuration import config


class ModelRepository:
    def __init__(self):
        self.settings = config.get_settings()

    def save_expected_returns(self, expected_returns: pd.DataFrame, date: datetime = datetime.today()):
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[self.settings.etf_expected_returns_collection]

            collection.drop()

            expected_returns['date'] = date

            document = expected_returns.to_dict('records')
            collection.insert_many(document)

    def get_expected_returns_for_isins(self, isins: List[str] = None, date: datetime = datetime.today()) -> pd.DataFrame:
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[self.settings.etf_expected_returns_collection]

            query = {
                # 'date': {'$lte': date}
            }

            projection = {
                '_id': 0
            }

            if isins is not None:
                for isin in isins:
                    projection[isin] = 1
            else:
                projection['date'] = 0

            # Find matching documents
            cursor = collection.find(query, projection).sort('date', pymongo.DESCENDING).limit(1)

            # Convert cursor to list of documents
            documents = list(cursor)

            return pd.DataFrame(documents)

    def save_covariance(self, covariance_df: pd.DataFrame, date: datetime = datetime.today()):
        """
            Save covariance matrix as individual documents (isin1, isin2, value)

            Args:
                covariance_df: DataFrame with ISINs as both index and columns
        """
        mask = np.triu(np.ones(covariance_df.shape, dtype=bool))

        # Apply mask and convert to long format in one operation
        upper_triangle_series = covariance_df.where(mask).stack()

        # Convert to DataFrame with proper column names
        result_df = upper_triangle_series.reset_index()
        result_df.columns = ['isin1', 'isin2', 'covariance']
        result_df['date'] = date

        # Convert DataFrame directly to list of dictionaries for MongoDB
        documents = result_df.to_dict('records')

        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[self.settings.etf_covariance_collection]

            collection.drop()

            result = collection.insert_many(documents)

            # Create indexes for efficient querying
            collection.create_index([("isin1", 1), ("isin2", 1)])

        return len(result.inserted_ids)

    def get_covariance_for_isins(self, isins: List[str] = None, date: datetime = datetime.today()) -> pd.DataFrame:
        """
            Query covariance submatrix for specific ISINs using MongoDB filtering

            Args:
                collection: MongoDB collection object
                target_isins: List of ISINs to extract

            Returns:
                DataFrame: Covariance submatrix
            """
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            collection = db[self.settings.etf_covariance_collection]
            # Query MongoDB to get only relevant covariance pairs
            if isins is not None:
                if hasattr(isins, 'tolist'):
                    isins = isins.tolist()
                elif isinstance(isins, np.ndarray):
                    isins = list(isins)

                query = {
                    "isin1": {"$in": isins},
                    "isin2": {"$in": isins},
                }
            else:
                query = {}

            cursor = collection.find(
                query
                ,{"isin1": 1, "isin2": 1, "covariance": 1, "_id": 0}
            )

            # Convert to DataFrame and pivot to matrix format
            df = pd.DataFrame(list(cursor))

            if df.empty:
                return pd.DataFrame(0.0, index=isins, columns=isins)

        # Create upper triangle matrix using pivot
        upper_matrix = df.pivot(index='isin1', columns='isin2', values='covariance')
        upper_matrix = upper_matrix.reindex(index=isins, columns=isins)

        # Expand to full symmetric matrix
        full_matrix = upper_matrix.fillna(upper_matrix.T)

        return full_matrix
