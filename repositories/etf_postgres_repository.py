import psycopg2
import pandas as pd
from datetime import datetime
from dateutil.relativedelta import relativedelta
from typing import List, Optional

from configuration import config, config_params
from repositories.interfaces import EtfRepositoryInterface


class EtfPostgresRepository(EtfRepositoryInterface):
    def __init__(self):
        self.settings = config.get_settings()
        self.connection_params = config_params.CONNECTION_PARAMS

    def get_connection(self):
        return psycopg2.connect(**self.connection_params)

    #just accumulating, without money market, without crypto
    def get_latest_prices_eur(self, last_date: Optional[datetime] = None) -> pd.DataFrame:
        last_date = last_date or datetime.now().date()
        with self.get_connection() as conn, conn.cursor() as cur:
            sql = """
                  WITH latest_dates AS (SELECT asset_listing_id, MAX(data_date) AS max_date
                                        FROM asset_listing_data
                                        WHERE data_date <= %s
                                        GROUP BY asset_listing_id)
                  SELECT d.asset_listing_id, l.asset_listing_code, d.data_date, d.adjusted_close_eur::float
                  FROM asset_listing_data d
                           JOIN latest_dates ld ON d.asset_listing_id = ld.asset_listing_id AND d.data_date = ld.max_date
                           JOIN asset_listings l ON d.asset_listing_id = l.id
                           JOIN asset_metadata m ON l.asset_isin = m.isin
                  WHERE m.distribution_policy = 'Accumulating'
                    AND m.asset_class_id NOT IN (5, 7)
                    AND l.is_valid = true
                  """
            cur.execute(sql, (last_date,))
            rows = cur.fetchall()
        if not rows:
            return pd.DataFrame()
        df = pd.DataFrame(rows, columns=['asset_listing_id', 'asset_listing_code', 'date', 'adjusted_close_eur'])
        return df.pivot(index='date', columns='asset_listing_code', values='adjusted_close_eur')

    def get_latest_prices_eur_for_asset_listing_codes(self,
                                                      asset_listing_codes: List[str],
                                                      last_date: Optional[datetime] = None) -> pd.DataFrame:
        last_date = last_date or datetime.now().date()
        with self.get_connection() as conn, conn.cursor() as cur:
            sql = """
                  WITH latest_dates AS (SELECT d.asset_listing_id, MAX(d.data_date) AS max_date
                                        FROM asset_listing_data d
                                                 JOIN asset_listings l ON d.asset_listing_id = l.id
                                        WHERE l.asset_listing_code = ANY (%s)
                                          AND d.data_date <= %s
                                        GROUP BY d.asset_listing_id)
                  SELECT d.asset_listing_id, l.asset_listing_code, d.data_date, d.adjusted_close_eur::float
                  FROM asset_listing_data d
                           JOIN latest_dates ld ON d.asset_listing_id = ld.asset_listing_id AND d.data_date = ld.max_date
                           JOIN asset_listings l ON d.asset_listing_id = l.id
                  WHERE l.asset_listing_code = ANY (%s) 
                  """
            cur.execute(sql, (asset_listing_codes, last_date, asset_listing_codes))
            rows = cur.fetchall()
        if not rows:
            return pd.DataFrame()
        df = pd.DataFrame(rows, columns=['asset_listing_id', 'asset_listing_code', 'date', 'adjusted_close_eur'])
        return df.pivot(index='date', columns='asset_listing_code', values='adjusted_close_eur')


    #just accumulating, without money market, without crypto
    def get_returns_eur_in_time_horizon(self, time_horizon_in_years: int, last_date: Optional[datetime] = None) -> pd.DataFrame:
        last_date = last_date or datetime.now().date()
        start_date = last_date - relativedelta(years=time_horizon_in_years)
        with self.get_connection() as conn, conn.cursor() as cur:
            sql = """
                  SELECT d.asset_listing_id, l.asset_listing_code, d.data_date, d.daily_return_eur::float
                  FROM asset_listing_data d
                           JOIN asset_listings l ON d.asset_listing_id = l.id
                           JOIN asset_metadata m ON l.asset_isin = m.isin
                  WHERE m.distribution_policy = 'Accumulating'
                    AND m.asset_class_id NOT IN (5, 7)
                    AND l.is_valid = true
                    AND d.data_date BETWEEN %s AND %s 
                  """
            cur.execute(sql, (start_date, last_date))
            rows = cur.fetchall()
        if not rows:
            return pd.DataFrame()
        df = pd.DataFrame(rows, columns=['asset_listing_id', 'asset_listing_code', 'date', 'daily_return_eur'])
        pivoted = df.pivot(index='date', columns='asset_listing_code', values='daily_return_eur')
        pivoted.sort_index(inplace=True)
        return pivoted

    def get_prices_eur_in_time_horizon_for_asset_listing_codes(self,
                                                               asset_listing_codes: List[str],
                                                               time_horizon_in_years: int,
                                                               ending_date: datetime) -> pd.DataFrame:
        start_date = ending_date - relativedelta(years=time_horizon_in_years)
        with self.get_connection() as conn, conn.cursor() as cur:
            sql = """
                  SELECT d.asset_listing_id, l.asset_listing_code, d.data_date, d.adjusted_close_eur::float
                  FROM asset_listing_data d
                           JOIN asset_listings l ON d.asset_listing_id = l.id
                  WHERE l.asset_listing_code = ANY (%s)
                    AND d.data_date BETWEEN %s AND %s \
                  """
            cur.execute(sql, (asset_listing_codes, start_date, ending_date))
            rows = cur.fetchall()
        if not rows:
            return pd.DataFrame()
        df = pd.DataFrame(rows, columns=['asset_listing_id', 'asset_listing_code', 'date', 'adjusted_close_eur'])
        return df.pivot(index='date', columns='asset_listing_code', values='adjusted_close_eur')

    def save_simulated_returns(self, simulated_returns: pd.DataFrame):
        now = datetime.now()
        records = []
        # simulated_returns index: (asset_listing_code, simulation_number)
        # columns: simulation_date(s)
        for (asset_listing_code, simulation_number), row in simulated_returns.iterrows():
            for simulation_date, simulated_return in row.items():
                records.append((
                    asset_listing_code,
                    simulation_number,
                    simulation_date,
                    float(simulated_return),
                    now,
                    now
                ))
        if not records:
            return
        with self.get_connection() as conn, conn.cursor() as cur:
            codes = list(set(r[0] for r in records))
            cur.execute(
                "SELECT asset_listing_code, id FROM asset_listings WHERE asset_listing_code = ANY(%s)",
                (codes,)
            )
            code_to_id = dict(cur.fetchall())
            db_records = []
            for code, simulation_number, simulation_date, simulated_return, created_at, updated_at in records:
                asset_listing_id = code_to_id.get(code)
                if asset_listing_id is not None:
                    db_records.append((
                        asset_listing_id,
                        simulation_number,
                        simulation_date,
                        simulated_return
                    ))
            if not db_records:
                return
            asset_ids = list(set(r[0] for r in db_records))
            simulation_dates = list(set(r[2] for r in db_records))
            cur.execute("""
                        DELETE
                        FROM asset_listing_return_simulations
                        WHERE asset_listing_id = ANY (%s)
                          AND simulation_date = ANY (%s)
                        """, (asset_ids, simulation_dates))
            args = ','.join(cur.mogrify("(%s,%s,%s,%s)", r).decode('utf-8') for r in db_records)
            cur.execute(f"""
                INSERT INTO asset_listing_return_simulations
                (asset_listing_id, simulation_number, simulation_date, simulated_return)
                VALUES {args}
            """)
            conn.commit()

    def get_simulations_for_asset_listing_codes(self,
                                                asset_listing_codes: List[str],
                                                n_simulations: Optional[int] = None) -> pd.DataFrame:
        with self.get_connection() as conn, conn.cursor() as cur:
            sql = """
                  SELECT s.asset_listing_id, l.asset_listing_code, s.simulation_number, s.simulation_date, s.simulated_return::float
                  FROM asset_listing_return_simulations s
                           JOIN asset_listings l ON s.asset_listing_id = l.id
                  WHERE l.asset_listing_code = ANY (%s) \
                  """
            params = [asset_listing_codes]
            if n_simulations:
                sql += " LIMIT %s"
                params.append(n_simulations)
            cur.execute(sql, tuple(params))
            rows = cur.fetchall()
        if not rows:
            return pd.DataFrame()
        df = pd.DataFrame(
            rows,
            columns=['asset_listing_id', 'asset_listing_code', 'simulation_number', 'simulation_date', 'simulated_return']
        )
        return df.pivot_table(
            index=['simulation_date', 'simulation_number'],
            columns='asset_listing_code',
            values='simulated_return'
        )
