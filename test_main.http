GET http://127.0.0.1:8000/productsMetadata
Accept: application/json
Content-Type: application/json



###
GET http://127.0.0.1:8000/getStatus
Accept: application/json
Content-Type: application/json



###
// @timeout 600
POST 127.0.0.1:8000/modelCalculations
Accept: application/json
Content-Type: application/json


###

# Test your FastAPI endpoints


POST http://127.0.0.1:8000/calculateEfficientFrontier
Accept: application/json
Content-Type: application/json

{
  "investmentAmount": 10000,
  "investmentHorizon": 120
}

###

// @timeout 600
POST http://127.0.0.1:8000/calculateMultiPeriodEfficientFrontier
Accept: application/json
Content-Type: application/json

{
  "investmentHorizon": 5,
  "cashFlows": [5000, 1000, 1000, 1000, 1000],
  "targetMeasure": "risk",
  "targetValue": 5000

}

###

// @timeout 600
POST http://127.0.0.1:8000/calculateMultiPeriodEfficientFrontier
Accept: application/json
Content-Type: application/json

{
  "investmentHorizon": 7,
  "cashFlows": [2000, 1000, 1000, 1000, 1000, 1000, 1000],
  "targetMeasure": "risk",
  "targetValue": 12000

}

###

// @timeout 1500
POST http://127.0.0.1:8000/calculateMultiPeriodEfficientFrontier
Accept: application/json
Content-Type: application/json

{
  "investmentHorizon": 15,
  "cashFlows": [10000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000],
  "targetMeasure": "return",
  "targetValue": 30000

}


###

POST http://127.0.0.1:8000/calculatePortfolioValues
Accept: application/json
Content-Type: application/json

{
  "initialInvestmentAmount": 10000,
  "endingDatetime": "2024-09-26T15:23:45.123456",
  "portfolio": [
    {
      "isin": "IE00BF4RFH31",
      "weight": 0.75,
      "quantity": 1
    },
    {
      "isin": "DE000A0S9GB0",
      "weight": 0.25,
      "quantity": 1
    }
  ],
  "timeHorizonInYears": 1
}


###

POST http://127.0.0.1:8000/rebalancePortfolio
Accept: application/json
Content-Type: application/json

{
  "cashFlow": 10000,
  "targetWeights": {
    "IE00BJ0KDR00": 0.08648084647180564,
    "IE00BF4RFH31": 0.006339714785436573,
    "IE00BYXPSP02": 0.02073138448101803,
    "IE00BGSF1X88": 0.0456461167413607,
    "IE00BF11F565": 0.03579262162324709,
    "IE00B3VWN518": 0.0009241120224073474,
    "LU0290355717": 0.0840903992158823,
    "IE00B1FZSC47": 0.05818951815468045,
    "DE000A0S9GB0": 0.040000853377484304,
    "IE00B4K48X80": 0.049992838209642204,
    "FR0010510800": 0.5718082106259048
  }
}


###

POST http://127.0.0.1:8000/rebalancePortfolio
Accept: application/json
Content-Type: application/json

{
  "cashFlow": 20000,
  "targetWeights": {
    "IE00BJ0KDR00": 0.5,
    "IE00BF4RFH31": 0.5
  }
}


###

POST http://127.0.0.1:8000/rebalancePortfolio
Accept: application/json
Content-Type: application/json

{
  "currentPortfolio": {
    "assetPurchaseLots": [
      {
        "isin": "IE00BJ0KDR00",
        "purchaseLots": [
          {
            "quantityPurchased": 62,
            "purchaseDate": "2025-05-02T10:00:00",
            "unitPurchasePrice": 153.61
          }
        ]
      },
      {
        "isin": "IE00BF4RFH31",
        "purchaseLots": [
          {
            "quantityPurchased": 1636,
            "purchaseDate": "2024-12-16T09:15:00",
            "unitPurchasePrice": 5.8641
          }
        ]
      }
    ]
  },
  "cashFlow": 0,
  "targetWeights": {
    "IE00BJ0KDR00": 0.5,
    "IE00BF4RFH31": 0.5
  },
  "rebalanceConstraints": {
    "allowedCapitalGains": 0,
    "allowedTradeCosts": 100
  }
}

###

POST http://127.0.0.1:8000/calculateMultiPeriodEfficientFrontier
Accept: application/json
Content-Type: application/json

{
  "investmentHorizon": 2,
  "cashFlows": [2000],
  "targetMeasure": "risk",
  "targetValue": 20000

}


###

POST http://127.0.0.1:8000/rebalancePortfolio
Accept: application/json
Content-Type: application/json

{
  "currentPortfolio": {
    "assetPurchaseLots": [
      {
        "isin": "IE00BJ0KDR00",
        "purchaseLots": [
          {
            "quantityPurchased": 6.0,
            "purchaseDate": "2025-06-26T14:22:15.287314",
            "unitPurchasePrice": 153.695
          },
          {
            "quantityPurchased": 4.0,
            "purchaseDate": "2025-06-26T14:22:36.870890",
            "unitPurchasePrice": 153.695
          }
        ]
      },
      {
        "isin": "IE00B4K48X80",
        "purchaseLots": []
      },
      {
        "isin": "LU1781541252",
        "purchaseLots": [
          {
            "quantityPurchased": 1.0,
            "purchaseDate": "2025-06-26T14:22:15.370140",
            "unitPurchasePrice": 16.513
          }
        ]
      },
      {
        "isin": "IE00BTJRMP35",
        "purchaseLots": []
      },
      {
        "isin": "IE00BF4RFH31",
        "purchaseLots": [
          {
            "quantityPurchased": 179.0,
            "purchaseDate": "2025-06-26T14:22:15.395596",
            "unitPurchasePrice": 5.8601
          },
          {
            "quantityPurchased": 67.0,
            "purchaseDate": "2025-06-26T14:22:36.970883",
            "unitPurchasePrice": 5.8601
          }
        ]
      },
      {
        "isin": "IE00BGSF1X88",
        "purchaseLots": []
      },
      {
        "isin": "IE00BYXPSP02",
        "purchaseLots": []
      },
      {
        "isin": "IE00B3VWN518",
        "purchaseLots": []
      },
      {
        "isin": "IE00B1FZSC47",
        "purchaseLots": []
      },
      {
        "isin": "IE00BYXYYJ35",
        "purchaseLots": []
      },
      {
        "isin": "IE00BF11F565",
        "purchaseLots": []
      },
      {
        "isin": "IE00BYXYYL56",
        "purchaseLots": []
      },
      {
        "isin": "LU0290355717",
        "purchaseLots": []
      },
      {
        "isin": "DE000A0S9GB0",
        "purchaseLots": []
      },
      {
        "isin": "LU1437018838",
        "purchaseLots": []
      },
      {
        "isin": "FR0010510800",
        "purchaseLots": []
      }
    ]
  },
  "cashFlow": 1.02,
  "targetWeights": {
    "IE00B1FZSC47": 0.0,
    "DE000A0S9GB0": 0.0,
    "FR0010510800": 0.0,
    "LU1781541252": 0.0,
    "LU0290355717": 0.0,
    "IE00B4K48X80": 0.0,
    "IE00BJ0KDR00": 0.4999999999999996,
    "IE00BYXYYL56": 0.0,
    "IE00BTJRMP35": 0.0,
    "IE00BF4RFH31": 0.5,
    "IE00BF11F565": 0.0,
    "IE00BGSF1X88": 0.0,
    "IE00B3VWN518": 0.0,
    "IE00BYXPSP02": 0.0,
    "IE00BYXYYJ35": 0.0,
    "LU1437018838": 0.0
  }
}


###
POST http://127.0.0.1:8000/rebalancePortfolio
Accept: application/json
Content-Type: application/json

{
  "currentPortfolio": {
    "assetPurchaseLots": [
      {"isin": "IE00BJ0KDR00", "purchaseLots": []},
      {"isin": "IE00B4K48X80", "purchaseLots": []},
      {"isin": "LU1781541252", "purchaseLots": []},
      {"isin": "IE00BTJRMP35", "purchaseLots": []},
      {"isin": "IE00BF4RFH31", "purchaseLots": []},
      {"isin": "IE00BGSF1X88", "purchaseLots": []},
      {"isin": "IE00BYXPSP02", "purchaseLots": []},
      {"isin": "IE00B3VWN518", "purchaseLots": []},
      {"isin": "IE00B1FZSC47", "purchaseLots": []},
      {"isin": "IE00BYXYYJ35", "purchaseLots": []},
      {"isin": "IE00BF11F565", "purchaseLots": []},
      {"isin": "IE00BYXYYL56", "purchaseLots": []},
      {"isin": "LU0290355717", "purchaseLots": []},
      {"isin": "DE000A0S9GB0", "purchaseLots": []},
      {"isin": "LU1437018838", "purchaseLots": []},
      {"isin": "FR0010510800", "purchaseLots": []}
    ]
  },
  "cashFlow": 2000.0,
  "targetWeights": {
    "IE00B1FZSC47": 0.0,
    "DE000A0S9GB0": 0.0,
    "FR0010510800": 0.0,
    "LU1781541252": 0.0,
    "LU0290355717": 0.0,
    "IE00B4K48X80": 0.0,
    "IE00BJ0KDR00": 0.5,
    "IE00BYXYYL56": 0.0,
    "IE00BTJRMP35": 0.0,
    "IE00BF4RFH31": 0.5,
    "IE00BF11F565": 0.0,
    "IE00BGSF1X88": 0.0,
    "IE00B3VWN518": 0.0,
    "IE00BYXPSP02": 0.0,
    "IE00BYXYYJ35": 0.0,
    "LU1437018838": 0.0
  }
}


###
POST http://127.0.0.1:8000/rebalancePortfolio
Accept: application/json
Content-Type: application/json

{
  "currentPortfolio": {
    "assetPurchaseLots": [
      {
        "isin": "IE00BJ0KDR00",
        "purchaseLots": [
          {
            "quantityPurchased": 6.0,
            "purchaseDate": "2025-06-27T10:26:26.007375",
            "unitPurchasePrice": 153.61
          }
        ]
      },
      {
        "isin": "IE00B4K48X80",
        "purchaseLots": []
      },
      {
        "isin": "LU1781541252",
        "purchaseLots": [
          {
            "quantityPurchased": 1.0,
            "purchaseDate": "2025-06-27T10:26:26.026509",
            "unitPurchasePrice": 16.688
          }
        ]
      },
      {
        "isin": "IE00BTJRMP35",
        "purchaseLots": []
      },
      {
        "isin": "IE00BF4RFH31",
        "purchaseLots": [
          {
            "quantityPurchased": 179.0,
            "purchaseDate": "2025-06-27T10:26:26.065832",
            "unitPurchasePrice": 5.8641
          }
        ]
      },
      {
        "isin": "IE00BGSF1X88",
        "purchaseLots": []
      },
      {
        "isin": "IE00BYXPSP02",
        "purchaseLots": []
      },
      {
        "isin": "IE00B3VWN518",
        "purchaseLots": []
      },
      {
        "isin": "IE00B1FZSC47",
        "purchaseLots": []
      },
      {
        "isin": "IE00BYXYYJ35",
        "purchaseLots": []
      },
      {
        "isin": "IE00BF11F565",
        "purchaseLots": []
      },
      {
        "isin": "IE00BYXYYL56",
        "purchaseLots": []
      },
      {
        "isin": "LU0290355717",
        "purchaseLots": []
      },
      {
        "isin": "DE000A0S9GB0",
        "purchaseLots": []
      },
      {
        "isin": "LU1437018838",
        "purchaseLots": []
      },
      {
        "isin": "FR0010510800",
        "purchaseLots": []
      }
    ]
  },
  "cashFlow": 2.98,
  "targetWeights": {
    "IE00B1FZSC47": 0.0,
    "DE000A0S9GB0": 0.0,
    "FR0010510800": 0.0,
    "LU1781541252": 0.0,
    "LU0290355717": 0.0,
    "IE00B4K48X80": 0.0,
    "IE00BJ0KDR00": 0.5,
    "IE00BYXYYL56": 0.0,
    "IE00BTJRMP35": 0.0,
    "IE00BF4RFH31": 0.5,
    "IE00BF11F565": 0.0,
    "IE00BGSF1X88": 0.0,
    "IE00B3VWN518": 0.0,
    "IE00BYXPSP02": 0.0,
    "IE00BYXYYJ35": 0.0,
    "LU1437018838": 0.0
  }
}


###

POST http://127.0.0.1:8000/rebalancePortfolio
Accept: application/json
Content-Type: application/json

{
  "currentPortfolio": {
    "assetPurchaseLots": []
  },
  "cashFlow": 2000.0,
  "targetWeights": {
    "FR0010510800": 1.0
  }
}

###

POST http://127.0.0.1:8000/modelCalculations2
Accept: application/json
Content-Type: application/json

