import pandas as pd
import numpy as np
from dateutil.relativedelta import relativedelta

from schemas.requests import BacktestRequest
from configuration import config
from repositories.old_mongo_repositories import etf_repository
from utils import backtest_utils


class BacktestService:
    def __init__(self):
        super(BacktestService, self).__init__()
        self.settings = config.get_settings()
        self.etf_repository = etf_repository.EtfRepository()


    def calculate_portfolio_values_without_rebalance(self, request: BacktestRequest) -> (pd.DataFrame, pd.DataFrame):
        initial_investment_amount = request.investmentAmount

        isins = backtest_utils.get_isins_from_portfolio(request.portfolio)

        initial_weights = backtest_utils.get_weights_from_portfolio(request.portfolio)

        initial_investments = initial_weights.values * initial_investment_amount

        returns = self.etf_repository.get_returns_with_risk_free_between_dates_for_isins(isins, request.startingDatetime, request.endingDatetime)

        cumulative_returns = np.cumprod(1 + returns, axis=0)

        investments = cumulative_returns.values * initial_investments

        values = cumulative_returns.values @ initial_investments.T

        weights = investments / values

        return returns.index.values, weights, values

    def calculate_portfolio_values(self, request: BacktestRequest) -> (pd.DataFrame, pd.DataFrame):
        initial_investment_amount = request.investmentAmount
        initial_weights = backtest_utils.get_weights_from_portfolio(request.portfolio).values
        initial_investments = initial_weights * initial_investment_amount

        isins = backtest_utils.get_isins_from_portfolio(request.portfolio)

        returns = self.etf_repository.get_returns_with_risk_free_between_dates_for_isins(isins, request.startingDatetime, request.endingDatetime)

        start_date = returns.index[0]

        difference = relativedelta(request.endingDatetime, request.startingDatetime)
        time_horizon = difference.years + difference.months / 12 + difference.days / 365.25

        rebalance_dates = pd.date_range(start=start_date, periods=np.ceil(time_horizon) + 1, freq='365D')

        intervals = list(zip(rebalance_dates[:-1], rebalance_dates[1:]))

        values = []
        weights = []

        # TODO fix intervals

        for (start, end) in intervals:
            returns_in_year = returns.loc[start:end].values

            cumulative_return_in_year = np.cumprod(1 + returns_in_year, axis=0)
            cumulative_return_in_year[0, :] = 1

            investments_in_year = cumulative_return_in_year * initial_investments

            values_in_year = np.sum(investments_in_year, axis=1).reshape(-1, 1)

            weights_in_year = investments_in_year / values_in_year

            values.extend(values_in_year)
            weights.extend(weights_in_year)

            initial_investments = initial_weights * values[-1]


        return returns.index.values, weights, values
