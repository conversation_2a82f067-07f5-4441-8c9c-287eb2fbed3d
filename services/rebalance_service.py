from abc import ABC, abstractmethod
from datetime import datetime

import numpy as np
import pandas as pd

from configuration import config, logging_config
from models import benchmark_model
from optimizers.rebalance_optimizer import RebalanceEngine
from repositories.old_mongo_repositories import etf_repository, risk_free_rate_repository
from schemas.pydantic_models import RebalancedAsset, RebalancedPortfolio, PortfolioPurchaseLots, AssetPurchaseLots
from schemas.requests import RebalanceRequest
from schemas.responses import RebalanceResponse
from services import model_service

logger = logging_config.get_logger(__name__)


class RebalanceServiceInterface(ABC):
    def __init__(self):
        self.settings = config.get_settings()
        self.rebalance_optimizer = RebalanceEngine()
        self.risk_free_rate_repository = risk_free_rate_repository.RiskFreeRateRepository()
        self.model = benchmark_model.RegressionPricingModel()
        self.etf_repository = etf_repository.EtfRepository()
        self.model_service = model_service.ModelService()

    @abstractmethod
    def rebalance_portfolio(self, request: RebalanceRequest) -> RebalanceResponse:
        pass


class RebalanceService(RebalanceServiceInterface):

    def __init__(self):
        super(RebalanceService, self).__init__()

    def rebalance_portfolio(self, request: RebalanceRequest) -> RebalanceResponse:
        date = datetime.today()

        prices_df, risk_free_price_df = self.etf_repository.get_prices_eur(date)
        prices_df = pd.concat([prices_df, risk_free_price_df], axis=1)

        isins = prices_df.columns.values

        weights = self._align_weights_with_isins(request.target_weights, isins)

        current_portfolio = self._get_current_portfolio(request.current_portfolio, isins)

        covariance = self.model_service.get_covariance(isins)

        rebalance_results = self.rebalance_optimizer.rebalance_portfolio(
            weights=weights,
            portfolio_purchase_lots=current_portfolio,
            new_prices=prices_df.values,
            covariance=covariance.to_numpy(),
            cash=request.cash_flow,
            calculate_on_date=date,
            allowed_trade_cost=request.rebalance_constraints.allowed_trade_cost,
            allowed_capital_gains=request.rebalance_constraints.allowed_capital_gains
        )

        if rebalance_results.delta_quantities is None or not rebalance_results.delta_quantities.size:
            return RebalanceResponse(rebalanced_portfolio=RebalancedPortfolio(rebalanced_assets=[], new_mark_to_market_portfolio_value=0, new_cash=0))

        new_quantities_dict = dict(zip(isins, rebalance_results.new_quantities))
        delta_quantities_dict = dict(zip(isins, rebalance_results.delta_quantities))
        new_weights_dict = dict(zip(isins, rebalance_results.new_weights))
        transaction_costs_dict = dict(zip(isins, rebalance_results.trade_costs))

        rebalanced_assets = [
            RebalancedAsset(
                isin=isin,
                new_quantity=new_quantities_dict[isin],
                delta_quantity=delta_quantities_dict[isin],
                new_weight=new_weights_dict[isin],
                capital_gain=rebalance_results.per_asset_cg.get(isin, 0.0),
                transaction_cost=transaction_costs_dict[isin]
            )
            for isin in isins
            if delta_quantities_dict[isin] != 0
        ]

        rebalanced_portfolio = RebalancedPortfolio(rebalanced_assets=rebalanced_assets, new_mark_to_market_portfolio_value=rebalance_results.new_portfolio_value, new_cash=rebalance_results.new_cash)
        response = RebalanceResponse(rebalanced_portfolio=rebalanced_portfolio)

        return response

    def _sort_and_fill_portfolio_transactions(self, portfolio_transactions: PortfolioPurchaseLots, isin_order: list[str]) -> PortfolioPurchaseLots:
        asset_dict = {asset.isin: asset for asset in portfolio_transactions.asset_purchase_lots}

        sorted_assets = []
        new_assets = []
        for isin in isin_order:
            if isin in asset_dict:
                sorted_assets.append(asset_dict[isin])
                new_assets.append(asset_dict[isin])
            else:
                zero_asset = AssetPurchaseLots(
                    isin=isin,
                    purchase_lots=[]
                )
                sorted_assets.append(zero_asset)

        sorted_portfolio_transactions = PortfolioPurchaseLots(asset_purchase_lots=sorted_assets)
        return sorted_portfolio_transactions

    def _align_weights_with_isins(self, target_weights: dict[str, float], isins: list[str]) -> np.ndarray:
        weights = np.array([target_weights.get(isin, 0.0) for isin in isins])
        return weights

    def _get_current_portfolio(self, current_portfolio: PortfolioPurchaseLots, isins: list[str]) -> PortfolioPurchaseLots:
        if current_portfolio is not None:
            current_portfolio = self._sort_and_fill_portfolio_transactions(current_portfolio, isins)
        else:
            current_portfolio = self._sort_and_fill_portfolio_transactions(PortfolioPurchaseLots(asset_purchase_lots=[]), isins)
        return current_portfolio
