from abc import ABC, abstractmethod
from typing import List

import pandas as pd

from configuration import config
from repositories.old_mongo_repositories import etf_repository
from services import model_service
from utils import simulation_utils


class SimulationServiceInterface(ABC):
    def __init__(self):
        self.settings = config.get_settings()
        self.etf_repository = etf_repository.EtfRepository()

        self._cached_simulations = None
        self._cached_isins = None
        self._cached_n_simulations = None

    def get_returns_simulations_for_isins(self, isins: List[str] = None, n_simulations: int = None) -> pd.DataFrame:
        if (self._cached_simulations is not None and
                self._cached_isins == isins and
                (n_simulations is None or self._cached_n_simulations >= n_simulations)):
            return self._cached_simulations[:n_simulations]

        simulations_df = self.etf_repository.get_simulations_for_isins(isins, n_simulations)

        self._cached_simulations = simulations_df
        self._cached_isins = isins
        self._cached_n_simulations = len(simulations_df)

        return simulations_df

    @abstractmethod
    def simulate_returns(self, isins: List[str] = None, n_simulations: int = 50000) -> pd.DataFrame:
        pass

    def save_simulated_returns(self, simulations_df: pd.DataFrame):
        self.etf_repository.save_simulated_returns(simulations_df)
        return

    def simulate_and_save_returns(self, isins: List[str] = None, n_simulations: int = 50000) -> pd.DataFrame:
        simulations_df = self.simulate_returns(isins, n_simulations)
        self.save_simulated_returns(simulations_df)
        return simulations_df


    def clear_cache(self):
        """Clear the cached simulation data"""
        self._cached_simulations = None
        self._cached_isins = None
        self._cached_n_simulations = None


class LogNormalSimulationService(SimulationServiceInterface):
    def __init__(self):
        super(LogNormalSimulationService, self).__init__()
        self.model_service = model_service.ModelService()

    def simulate_returns(self, isins: List[str] = None, n_simulations: int = 50000) -> pd.DataFrame:
        expected_returns, covariance = self.model_service.get_expected_returns_and_covariance(isins)
        covariance = covariance.reindex(index=expected_returns.columns, columns=expected_returns.columns)
        simulations = simulation_utils.sample_log_normal_from_arithmetic_parameters(expected_returns.values, covariance.values, n_simulations)
        return pd.DataFrame(simulations, columns=expected_returns.columns)
