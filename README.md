To install requirements with pip use 'pip install -r requirements_pip.txt' in your environment.
To install requirements with conda use 'conda env create -n new-env-name -f Valusense.yml'.

API needs MongoDB database with metadata. 
Easiest way to do it is to create local database on default url 'mongodb://localhost:27017/', 
create db with name 'Valusense' and collections named 'Metada<PERSON>', 'Asset_classes_market_caps', 'Asset_classes_returns', 'ETF_prices', 'Indices_returns'.
If you change anything of that you will need to change config file in the project.

After creating database you'll need to import .csv files into collections.
In 'Asset_classes_market_caps' collection import 'Asset Classes Market Caps.csv' from data/asset_classes_data/market_capitalization.
In 'Asset_classes_returns' collection import 'Asset Classes Returns.csv' from data/asset_classes_data/returns
In 'ETF_prices' collection import 'ETF prices.csv' from data/ETF_data/prices
In 'ETF_returns' collection import 'ETF Indices Returns.csv' from data/ETF_indices_data/returns
In 'Metadata' collection import 'metadata.json' from data/ETF_metadata

Also, if your database is not on default localhost, you'll need to create .env file and insert database url in variable DB_URL (e.g. DB_URL='mongodb://localhost:27017/').


