import json

from fastapi import <PERSON><PERSON><PERSON>, Depends, Request, status
from typing import List, Optional
import logging

from starlette.concurrency import iterate_in_threadpool
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from models.benchmark_model_postgres import RegressionPricingModel
from repositories.asset_class_postgres_repository import AssetClassPostgresRepository
from repositories.etf_postgres_repository import EtfPostgresRepository
from repositories.metadata_postgres_repository import MetadataPostgresRepository
from repositories.risk_free_rate_postgres_repository import RiskFreeRatePostgresRepository
from schemas.requests import EfficientFrontierRequest, BacktestRequest, MultiPeriodEfficientFrontierRequest, RebalanceRequest
from schemas.responses import EfficientFrontierResponse, BacktestResponse, MultiPeriodEfficientFrontierResponse, RebalanceResponse
from services.backtest_service import BacktestService
from services.efficient_frontier_service import PortfolioEfficientFrontierServiceInterface, PortfolioEfficientFrontierService, StrategyEfficientFrontierServiceInterface, StrategyEfficientFrontierService
from services.model_service import ModelService
from services.simulation_service import SimulationServiceInterface, LogNormalSimulationService
from services.rebalance_service import RebalanceService
from utils import util
from schemas.pydantic_models import ProductMetadata
from configuration import logging_config, config
from errors import errors, error_handlers


class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        client_ip = request.client.host
        method = request.method
        url = request.url.path
        logger.info(f"Request: {method} {url} from {client_ip}")
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > 0:
            try:
                request_body = await request.json()
                logger.info(f"Request body: {json.dumps(request_body)}")
            except Exception:
                # Handle non-JSON or empty body
                logger.info("Request body: <not JSON or empty>")
        else:
            logger.info("Request body: <empty>")
        response = await call_next(request)
        status_code = response.status_code
        logger.info(f"Response: {method} {url} returned {status_code} to {client_ip}")
        res_body = [section async for section in response.body_iterator]
        response.body_iterator = iterate_in_threadpool(iter(res_body))
        if res_body:
            res_body = res_body[0].decode()
            logger.info(f"Response body: {res_body}")
        else:
            logger.info("Response body: <empty>")
        return response

app = FastAPI(
    title='Valusense Models API',
    version='0.0.1',
    contact={
        'name': 'Valusense',
        'url': 'https://valusense.eu/',
        'email': '<EMAIL>'
    }
)
app.add_middleware(LoggingMiddleware)
app.add_exception_handler(errors.OptimizationError, error_handlers.optimization_exception_handler)

logger = logging_config.get_logger(__name__)
logging.getLogger('pymongo').setLevel(logging.WARNING)


def get_efficient_frontier_service() -> PortfolioEfficientFrontierServiceInterface:
    return PortfolioEfficientFrontierService()

def get_multi_period_service() -> StrategyEfficientFrontierServiceInterface:
    return StrategyEfficientFrontierService()


def get_backtest_service() -> BacktestService:
    return BacktestService()

def get_model_service() -> ModelService:
    etf_repo = EtfPostgresRepository()
    metadata_repo = MetadataPostgresRepository()
    risk_free_rate_repo = RiskFreeRatePostgresRepository()
    asset_class_repo = AssetClassPostgresRepository()
    model = RegressionPricingModel(
        etf_repository=etf_repo,
        metadata_repository=metadata_repo,
        risk_free_rate_repository=risk_free_rate_repo,
        asset_class_repository=asset_class_repo,
        settings=config.get_settings()
    )
    return ModelService(
        risk_free_rate_repository=risk_free_rate_repo,
        model=model,
        settings=config.get_settings()
    )

def get_simulations_service() -> SimulationServiceInterface:
    return LogNormalSimulationService()

def get_metadata_repository() -> MetadataPostgresRepository:
    return MetadataPostgresRepository()


def get_rebalance_service() -> RebalanceService:
    return RebalanceService()

@app.get('/getStatus', status_code=status.HTTP_200_OK)
def get_status():
    return


@app.post('/modelCalculations')
def calculate_and_save_models_and_simulations(
        model_service: ModelService = Depends(get_model_service),
        simulations_service: SimulationServiceInterface = Depends(get_simulations_service),
):
    expected_returns, covariance = model_service.calculate_and_save_product_returns_and_covariance()
    simulations_df = simulations_service.simulate_and_save_returns()

    return expected_returns, covariance, simulations_df

@app.post('/modelCalculations2')
def calculate_and_save_models_and_simulations(
        model_service: ModelService = Depends(get_model_service)
):
    expected_returns, covariance = model_service.calculate_product_returns_and_covariance()

    return expected_returns, covariance


@app.get('/productsMetadata', response_model=List[ProductMetadata])
def get_products_metadata(
        ISINs: Optional[List[str]] = None,
        metadata_repository: MetadataPostgresRepository = Depends(get_metadata_repository)
):
    return metadata_repository.get_metadata_for_ISINs(ISINs)



#TODO move all except response in service
@app.post('/calculateEfficientFrontier', response_model=EfficientFrontierResponse)
def calculate_efficient_frontier(
        request: EfficientFrontierRequest,
        efficient_frontier_service: PortfolioEfficientFrontierServiceInterface = Depends(get_efficient_frontier_service)
):
    quantities_discrete_efficient_frontier_df, weights_discrete_efficient_frontier_df, returns, covariance, optimal_index = efficient_frontier_service.calculate_efficient_frontier(
        request, False)

    products_metadata = util.get_products_metadata(quantities_discrete_efficient_frontier_df.columns.values)
    portfolios = util.create_portfolios(quantities_discrete_efficient_frontier_df,
                                        weights_discrete_efficient_frontier_df)
    return EfficientFrontierResponse(productsMetadata=products_metadata,
                                     annualReturns=returns,
                                     covarianceMatrix=covariance,
                                     frontier=portfolios,
                                     optimalIndex=optimal_index)


@app.post('/calculateMultiPeriodEfficientFrontier', response_model=MultiPeriodEfficientFrontierResponse)
def calculate_multi_period_efficient_frontier(
        request: MultiPeriodEfficientFrontierRequest,
        efficient_frontier_service: StrategyEfficientFrontierServiceInterface = Depends(get_multi_period_service)):
    return efficient_frontier_service.calculate_efficient_frontier(request)


@app.post('/calculatePortfolioValues', response_model=BacktestResponse)
def calculate_portfolio_values(
        request: BacktestRequest,
        backtest_service: BacktestService = Depends(get_backtest_service),
        rebalance: bool = True):
    if rebalance:
        date_times, weights, values = backtest_service.calculate_portfolio_values(request)
    else:
        date_times, weights, values = backtest_service.calculate_portfolio_values_without_rebalance(request)
    response = util.create_backtest_response(date_times, weights, values)
    return response

@app.post('/rebalancePortfolio', response_model=RebalanceResponse)
def rebalance_portfolio(
        request: RebalanceRequest,
        rebalance_service: RebalanceService = Depends(get_rebalance_service)):

    response = rebalance_service.rebalance_portfolio(request)

    if not response.rebalanced_portfolio.rebalanced_assets:
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    return response
